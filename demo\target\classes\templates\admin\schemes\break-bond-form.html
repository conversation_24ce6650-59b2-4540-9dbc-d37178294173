<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Break Bond</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #dc3545;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/schemes/applications}">
                            <i class="bi bi-file-earmark-text"></i> Scheme Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes}">
                            <i class="bi bi-list-check"></i> Manage Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/documents}">
                            <i class="bi bi-file-earmark"></i> Document Types
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2><i class="bi bi-scissors"></i> Break Bond</h2>
                    <p>This action is irreversible and will break the bond between the user and NetCafe</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <i class="bi bi-exclamation-triangle-fill"></i> Warning
                    </div>
                    <div class="card-body">
                        <p class="fw-bold">Breaking the bond is irreversible and will:</p>
                        <ul>
                            <li>Mark the application as "BOND_BROKEN" in the system</li>
                            <li>Notify the user and NetCafe about the bond breaking</li>
                            <li>Release any pending obligations between the parties</li>
                        </ul>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        Application Information
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Application ID:</strong> <span th:text="${application.id}">1</span></p>
                                <p><strong>Application Date:</strong> <span th:text="${#temporals.format(application.applicationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                <p><strong>Status:</strong> <span th:text="${application.status}">PENDING</span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>User:</strong> <span th:text="${application.user != null ? application.user.name : 'N/A'}">John Doe</span></p>
                                <p><strong>Scheme:</strong> <span th:text="${application.scheme != null ? application.scheme.name : 'N/A'}">Scheme Name</span></p>
                                <p><strong>NetCafe:</strong> <span th:text="${netCafeApplication.netCafeUser != null ? netCafeApplication.netCafeUser.name : 'N/A'}">NetCafe Name</span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        Break Bond Form
                    </div>
                    <div class="card-body">
                        <form th:action="@{/admin/schemes/applications/{id}/break-bond(id=${application.id})}" method="post">
                            <div class="mb-3">
                                <label for="reason" class="form-label">Reason for breaking bond <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="reason" name="reason" rows="5" required placeholder="Please provide a detailed reason for breaking the bond..."></textarea>
                                <div class="form-text">This reason will be visible to both the user and NetCafe.</div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <a th:href="@{/admin/schemes/applications/{id}(id=${application.id})}" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-danger">
                                    <i class="bi bi-scissors"></i> Break Bond
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
