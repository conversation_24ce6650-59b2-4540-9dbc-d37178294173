<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NetCafe Registration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .form-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-top: 30px;
            margin-bottom: 30px;
        }
        .form-title {
            color: #007bff;
            margin-bottom: 30px;
            text-align: center;
        }
        .btn-primary {
            background-color: #007bff;
            border: none;
        }
        .btn-primary:hover {
            background-color: #0069d9;
        }
        .form-label {
            font-weight: 500;
        }
        .alert {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="form-container">
                    <h2 class="form-title">NetCafe Registration</h2>

                    <div th:if="${error}" class="alert alert-danger" role="alert" th:text="${error}"></div>

                    <form th:action="@{/netcafe/register}" method="post" enctype="multipart/form-data" th:object="${user}">
                        <div class="mb-3">
                            <label for="name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="name" th:field="*{name}" required>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" th:field="*{email}" required>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" th:field="*{password}" required>
                        </div>

                        <div class="mb-3">
                            <label for="mobileNumber" class="form-label">Mobile Number</label>
                            <input type="text" class="form-control" id="mobileNumber" th:field="*{mobileNumber}" required>
                        </div>

                        <div class="mb-3">
                            <label for="aadharNumber" class="form-label">Aadhar Card Number</label>
                            <input type="text" class="form-control" id="aadharNumber" th:field="*{aadharNumber}" required>
                        </div>

                        <div class="mb-3">
                            <label for="aadharCardPhotoFile" class="form-label">Aadhar Card Photo</label>
                            <input type="file" class="form-control" id="aadharCardPhotoFile" name="aadharCardPhotoFile" required>
                        </div>

                        <div class="mb-3">
                            <label for="panNumber" class="form-label">PAN Card Number</label>
                            <input type="text" class="form-control" id="panNumber" th:field="*{panNumber}" required>
                        </div>

                        <div class="mb-3">
                            <label for="panCardPhotoFile" class="form-label">PAN Card Photo</label>
                            <input type="file" class="form-control" id="panCardPhotoFile" name="panCardPhotoFile" required>
                        </div>

                        <div class="mb-3">
                            <label for="cscCertificateFile" class="form-label">CSC Certificate</label>
                            <input type="file" class="form-control" id="cscCertificateFile" name="cscCertificateFile" required>
                        </div>

                        <div class="mb-3">
                            <label for="photoFile" class="form-label">Your Photo</label>
                            <input type="file" class="form-control" id="photoFile" name="photoFile" required>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">Register</button>
                        </div>
                    </form>

                    <div class="text-center mt-3">
                        <p>Already have an account? <a th:href="@{/netcafe/login}">Login here</a></p>
                        <p><a th:href="@{/}">Back to Home</a></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
