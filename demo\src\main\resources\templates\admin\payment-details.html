<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Details - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #0d6efd;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .payment-details {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .payment-amount {
            font-size: 2rem;
            font-weight: bold;
            color: #0d6efd;
        }
        .screenshot-container {
            max-width: 100%;
            overflow: hidden;
            margin: 20px 0;
            text-align: center;
        }
        .screenshot-img {
            max-width: 100%;
            height: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/pending-payments}">
                            <i class="bi bi-credit-card"></i> Pending Payments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 content">
                <div class="header-card">
                    <h2>Payment Details</h2>
                    <p>Review and verify payment information</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Payment Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="payment-details">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <p><strong>Payment ID:</strong> <span th:text="${payment.id}">1</span></p>
                                            <p><strong>User:</strong> <span th:text="${payment.user != null ? payment.user.name : 'Unknown'}">User Name</span></p>
                                            <p><strong>Email:</strong> <span th:text="${payment.user != null ? payment.user.email : 'Unknown'}"><EMAIL></span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Date:</strong> <span th:text="${#temporals.format(payment.paymentDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                            <p><strong>Status:</strong> <span class="badge bg-warning">Pending</span></p>
                                            <p><strong>Payment Method:</strong> <span th:text="${payment.paymentMethod}">UPI</span></p>
                                        </div>
                                    </div>
                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <p><strong>Scheme:</strong> <span th:text="${payment.application != null && payment.application.scheme != null ? payment.application.scheme.name : 'Unknown'}">Scheme Name</span></p>
                                            <p>
                                                <strong>Application ID:</strong>
                                                <span th:text="${payment.application != null ? payment.application.id : 'Unknown'}">123</span>
                                                <a th:if="${payment.application != null}" th:href="@{/admin/schemes/applications/{id}(id=${payment.application.id})}" class="btn btn-sm btn-outline-primary ms-2">
                                                    <i class="bi bi-file-earmark-text"></i> View Application
                                                </a>
                                            </p>
                                            <p><strong>Transaction ID:</strong> <span th:text="${payment.transactionId}">TXN123456</span></p>
                                            <p><strong>Payment Method:</strong> <span th:text="${payment.paymentMethod}">UPI</span></p>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h5 class="mb-3">Amount</h5>
                                            <p class="payment-amount">₹<span th:text="${payment.amount}">500.00</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>Payment Screenshot</h5>
                            </div>
                            <div class="card-body">
                                <div class="screenshot-container">
                                    <img th:if="${payment.application != null && payment.application.paymentScreenshot != null}"
                                         th:src="@{/admin/application-payment-screenshot/{id}(id=${payment.application.id})}"
                                         alt="Payment Screenshot" class="screenshot-img">
                                    <p th:if="${payment.application == null || payment.application.paymentScreenshot == null}" class="text-muted">
                                        No payment screenshot available
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5>Verify Payment</h5>
                            </div>
                            <div class="card-body">
                                <form th:action="@{/admin/verify-payment/{id}(id=${payment.id})}" method="post">
                                    <div class="mb-3">
                                        <label for="remarks" class="form-label">Verification Remarks (Optional)</label>
                                        <textarea class="form-control" id="remarks" name="remarks" rows="3" placeholder="Add any notes about this payment verification"></textarea>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-success">
                                            <i class="bi bi-check-circle"></i> Verify Payment
                                        </button>
                                        <a th:href="@{/admin/pending-payments}" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-left"></i> Back to Pending Payments
                                        </a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
