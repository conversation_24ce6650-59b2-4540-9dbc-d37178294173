package com.example.demo.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class QRCodeGenerator {

    /**
     * Generate QR code as byte array
     * 
     * @param text The text or URL to encode in the QR code
     * @param width The width of the QR code image
     * @param height The height of the QR code image
     * @return Byte array containing the QR code image
     * @throws WriterException If there is an error generating the QR code
     * @throws IOException If there is an error writing the QR code
     */
    public static byte[] generateQRCode(String text, int width, int height) throws WriterException, IOException {
        // Create hints for QR code generation
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, 1);

        // Create QR code writer
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height, hints);

        // Write QR code to byte array
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        MatrixToImageWriter.writeToStream(bitMatrix, "PNG", outputStream);
        
        return outputStream.toByteArray();
    }
    
    /**
     * Generate a payment QR code with UPI details
     * 
     * @param upiId The UPI ID to receive payment
     * @param name The name of the receiver
     * @param amount The amount to be paid
     * @param description The description or purpose of payment
     * @return Byte array containing the QR code image
     * @throws WriterException If there is an error generating the QR code
     * @throws IOException If there is an error writing the QR code
     */
    public static byte[] generateUpiQRCode(String upiId, String name, double amount, String description) throws WriterException, IOException {
        // Format UPI URL
        String upiUrl = String.format("upi://pay?pa=%s&pn=%s&am=%s&tn=%s", 
                upiId, 
                name.replace(" ", "%20"), 
                amount, 
                description.replace(" ", "%20"));
        
        return generateQRCode(upiUrl, 300, 300);
    }
}
