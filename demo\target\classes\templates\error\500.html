<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Error</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .error-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            text-align: center;
            max-width: 500px;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 20px;
        }
        .btn-primary {
            background-color: #007bff;
            border: none;
        }
        .btn-primary:hover {
            background-color: #0069d9;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-code">500</div>
        <h1 class="mb-4">Internal Server Error</h1>
        <p class="lead mb-4">Something went wrong on our end. Please try again later.</p>

        <div class="alert alert-danger" th:if="${message}">
            <strong>Error:</strong> <span th:text="${message}">Error message</span>
        </div>

        <div class="alert alert-info" th:if="${path}">
            <strong>Path:</strong> <span th:text="${path}">Error path</span>
        </div>

        <div class="mt-3 mb-3 text-start" th:if="${trace}">
            <details>
                <summary>Stack Trace</summary>
                <pre class="bg-light p-3 mt-2" style="max-height: 300px; overflow-y: auto;" th:text="${trace}">Stack trace</pre>
            </details>
        </div>

        <a href="/" class="btn btn-primary">Go to Home Page</a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
