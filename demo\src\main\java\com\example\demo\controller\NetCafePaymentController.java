package com.example.demo.controller;

import com.example.demo.model.NetCafeApplication;
import com.example.demo.model.NetCafeUser;
import com.example.demo.model.PaymentRecord;
import com.example.demo.service.NetCafeApplicationService;
import com.example.demo.service.PaymentService;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/netcafe/payments")
public class NetCafePaymentController {

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private NetCafeApplicationService netCafeApplicationService;

    @GetMapping
    public String viewPaymentHistory(HttpSession session, Model model) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        List<PaymentRecord> payments = paymentService.getNetCafePaymentHistory(user);
        BigDecimal unsettledAmount = paymentService.getTotalUnsettledAmountForNetCafe(user);
        
        model.addAttribute("user", user);
        model.addAttribute("payments", payments);
        model.addAttribute("unsettledAmount", unsettledAmount);
        
        return "netcafe/payment-history";
    }

    @GetMapping("/application/{id}")
    public String viewApplicationPayments(@PathVariable Long id, HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        Optional<NetCafeApplication> applicationOpt = netCafeApplicationService.getApplicationById(id);
        
        if (applicationOpt.isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "Application not found");
            return "redirect:/netcafe/applications";
        }
        
        NetCafeApplication netCafeApplication = applicationOpt.get();
        
        // Verify that this application belongs to the current NetCafe user
        if (!netCafeApplication.getNetCafeUser().getId().equals(user.getId())) {
            redirectAttributes.addFlashAttribute("error", "You don't have permission to view this application's payments");
            return "redirect:/netcafe/applications";
        }
        
        List<PaymentRecord> payments = paymentService.getPaymentsForApplication(netCafeApplication.getApplication());
        
        model.addAttribute("user", user);
        model.addAttribute("netCafeApplication", netCafeApplication);
        model.addAttribute("application", netCafeApplication.getApplication());
        model.addAttribute("payments", payments);
        
        return "netcafe/application-payments";
    }

    @GetMapping("/filter")
    public String filterPayments(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            HttpSession session, 
            Model model) {
        
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }
        
        List<PaymentRecord> payments;
        
        if (startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty()) {
            LocalDateTime start = LocalDate.parse(startDate).atStartOfDay();
            LocalDateTime end = LocalDate.parse(endDate).atTime(LocalTime.MAX);
            
            payments = paymentService.getPaymentsByDateRange(start, end);
            // Filter to only show this NetCafe user's payments
            payments = payments.stream()
                .filter(p -> p.getNetCafeUser() != null && p.getNetCafeUser().getId().equals(user.getId()))
                .toList();
        } else {
            payments = paymentService.getNetCafePaymentHistory(user);
        }
        
        BigDecimal unsettledAmount = paymentService.getTotalUnsettledAmountForNetCafe(user);
        
        model.addAttribute("user", user);
        model.addAttribute("payments", payments);
        model.addAttribute("unsettledAmount", unsettledAmount);
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        
        return "netcafe/payment-history";
    }

    @GetMapping("/unsettled")
    public String viewUnsettledPayments(HttpSession session, Model model) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        List<PaymentRecord> unsettledPayments = paymentService.getNetCafePaymentHistory(user).stream()
            .filter(p -> !p.getIsSettled() && "NETCAFE_COMMISSION".equals(p.getPaymentType()))
            .toList();
        
        BigDecimal totalUnsettledAmount = paymentService.getTotalUnsettledAmountForNetCafe(user);
        
        model.addAttribute("user", user);
        model.addAttribute("payments", unsettledPayments);
        model.addAttribute("totalAmount", totalUnsettledAmount);
        
        return "netcafe/unsettled-payments";
    }
}
