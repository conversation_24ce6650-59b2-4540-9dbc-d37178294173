package com.example.demo.repository;

import com.example.demo.model.PaymentRecord;
import com.example.demo.model.GeneralUser;
import com.example.demo.model.NetCafeUser;
import com.example.demo.model.SchemeApplication;
import com.example.demo.model.NetCafeApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PaymentRecordRepository extends JpaRepository<PaymentRecord, Long> {

    // Find payments by user
    List<PaymentRecord> findByUserOrderByPaymentDateDesc(GeneralUser user);

    // Find payments by NetCafe user
    List<PaymentRecord> findByNetCafeUserOrderByPaymentDateDesc(NetCafeUser netCafeUser);

    // Find payments by application
    List<PaymentRecord> findByApplicationOrderByPaymentDateDesc(SchemeApplication application);

    // Find payments by NetCafe application
    List<PaymentRecord> findByNetCafeApplicationOrderByPaymentDateDesc(NetCafeApplication netCafeApplication);

    // Find payments by type
    List<PaymentRecord> findByPaymentTypeOrderByPaymentDateDesc(String paymentType);

    // Find payments by status
    List<PaymentRecord> findByStatusOrderByPaymentDateDesc(String status);

    // Find payments by payment type and status
    List<PaymentRecord> findByPaymentTypeAndStatusOrderByPaymentDateDesc(String paymentType, String status);

    // Find unsettled payments for NetCafe user
    List<PaymentRecord> findByNetCafeUserAndIsSettledFalseOrderByPaymentDateDesc(NetCafeUser netCafeUser);

    // Find payments by date range
    List<PaymentRecord> findByPaymentDateBetweenOrderByPaymentDateDesc(LocalDateTime startDate, LocalDateTime endDate);

    // Find payments by user and date range
    List<PaymentRecord> findByUserAndPaymentDateBetweenOrderByPaymentDateDesc(GeneralUser user, LocalDateTime startDate, LocalDateTime endDate);

    // Find payments by NetCafe user and date range
    List<PaymentRecord> findByNetCafeUserAndPaymentDateBetweenOrderByPaymentDateDesc(NetCafeUser netCafeUser, LocalDateTime startDate, LocalDateTime endDate);

    // Find recent payments by user
    @Query("SELECT p FROM PaymentRecord p WHERE p.user = ?1 ORDER BY p.paymentDate DESC")
    List<PaymentRecord> findRecentPaymentsByUser(GeneralUser user);

    // Find recent payments by NetCafe user
    @Query("SELECT p FROM PaymentRecord p WHERE p.netCafeUser = ?1 ORDER BY p.paymentDate DESC")
    List<PaymentRecord> findRecentPaymentsByNetCafeUser(NetCafeUser netCafeUser);

    // Find total unsettled amount for NetCafe user
    @Query("SELECT SUM(p.amount) FROM PaymentRecord p WHERE p.netCafeUser = ?1 AND p.isSettled = false AND p.paymentType = 'NETCAFE_COMMISSION'")
    Double findTotalUnsettledAmountForNetCafeUser(NetCafeUser netCafeUser);
}
