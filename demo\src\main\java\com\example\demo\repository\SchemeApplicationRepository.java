package com.example.demo.repository;

import com.example.demo.model.GeneralUser;
import com.example.demo.model.Scheme;
import com.example.demo.model.SchemeApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SchemeApplicationRepository extends JpaRepository<SchemeApplication, Long> {

    List<SchemeApplication> findByUser(GeneralUser user);

    List<SchemeApplication> findByScheme(Scheme scheme);

    List<SchemeApplication> findByStatus(String status);

    List<SchemeApplication> findByUserAndScheme(GeneralUser user, Scheme scheme);

    List<SchemeApplication> findByUserOrderByApplicationDateDesc(GeneralUser user);

    long countByStatus(String status);
}
