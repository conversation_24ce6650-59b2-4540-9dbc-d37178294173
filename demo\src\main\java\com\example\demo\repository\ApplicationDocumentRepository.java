package com.example.demo.repository;

import com.example.demo.model.ApplicationDocument;
import com.example.demo.model.Document;
import com.example.demo.model.SchemeApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ApplicationDocumentRepository extends JpaRepository<ApplicationDocument, Long> {
    
    List<ApplicationDocument> findByApplication(SchemeApplication application);
    
    Optional<ApplicationDocument> findByApplicationAndDocument(SchemeApplication application, Document document);
    
    void deleteByApplication(SchemeApplication application);
}
