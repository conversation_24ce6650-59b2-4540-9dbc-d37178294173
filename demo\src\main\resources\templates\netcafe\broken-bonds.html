<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Broken Bonds</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #0d6efd;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-completed {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-confirmed {
            background-color: #0d6efd;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-rejected {
            background-color: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-bond-broken {
            background-color: #6c757d;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .receipt-id {
            font-family: monospace;
            font-size: 0.9rem;
            font-weight: bold;
            color: #0d6efd;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>NetCafe Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/available-applications}">
                            <i class="bi bi-list-check"></i> Available Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/payments}">
                            <i class="bi bi-credit-card"></i> Payment History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/application-history}">
                            <i class="bi bi-clock-history"></i> Application History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/netcafe/broken-bonds}">
                            <i class="bi bi-scissors"></i> Broken Bonds
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Broken Bonds</h2>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>
                <div class="alert alert-info" role="alert" th:if="${info}" th:text="${info}"></div>

                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-scissors"></i> Applications with Broken Bonds
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Scheme</th>
                                        <th>User</th>
                                        <th>Application Date</th>
                                        <th>Completion Date</th>
                                        <th>Bond Broken Date</th>
                                        <th>Reason</th>
                                        <th>Initiated By</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:if="${brokenBondHistory.empty}">
                                        <td colspan="7" class="text-center">No broken bonds found</td>
                                    </tr>
                                    <tr th:each="app : ${brokenBondHistory}">
                                        <td th:text="${app.schemeName}">Scheme Name</td>
                                        <td th:text="${app.userName}">User Name</td>
                                        <td th:text="${#temporals.format(app.applicationDate, 'dd-MM-yyyy')}">01-01-2023</td>
                                        <td th:text="${app.completionDate != null ? #temporals.format(app.completionDate, 'dd-MM-yyyy') : 'N/A'}">02-01-2023</td>
                                        <td th:text="${app.bondBrokenDate != null ? #temporals.format(app.bondBrokenDate, 'dd-MM-yyyy') : 'N/A'}">03-01-2023</td>
                                        <td th:text="${app.bondBreakReason != null ? app.bondBreakReason : 'Bond broken'}">Reason</td>
                                        <td th:text="${app.bondBreakInitiator == 'NETCAFE' ? 'You' : 'User'}">Initiator</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card mt-4" th:if="${!brokenBondHistory.empty && brokenBondHistory.size() > 0}">
                    <div class="card-header">
                        <i class="bi bi-info-circle"></i> Information
                    </div>
                    <div class="card-body">
                        <p>This page shows your applications where the bond with a user has been broken. These applications are no longer active and are kept for historical purposes only.</p>
                        <p>When a bond is broken, it means that the relationship between you and the user for this specific application has been terminated. This can happen for various reasons, such as completion of the application process, disputes, or other issues.</p>
                        <p>If you have any questions about a broken bond, please contact our support team.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
