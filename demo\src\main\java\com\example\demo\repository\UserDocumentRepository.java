package com.example.demo.repository;

import com.example.demo.model.Document;
import com.example.demo.model.GeneralUser;
import com.example.demo.model.UserDocument;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserDocumentRepository extends JpaRepository<UserDocument, Long> {
    
    List<UserDocument> findByUser(GeneralUser user);
    
    List<UserDocument> findByUserOrderByUploadDateDesc(GeneralUser user);
    
    List<UserDocument> findByDocument(Document document);
    
    Optional<UserDocument> findByUserAndDocument(GeneralUser user, Document document);
    
    List<UserDocument> findByIsVerified(Boolean isVerified);
    
    long countByUser(GeneralUser user);
    
    long countByIsVerified(Boolean isVerified);
}
