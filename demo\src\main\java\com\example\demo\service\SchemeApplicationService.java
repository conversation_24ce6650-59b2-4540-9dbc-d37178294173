package com.example.demo.service;

import com.example.demo.model.ApplicationDocument;
import com.example.demo.model.Document;
import com.example.demo.model.GeneralUser;
import com.example.demo.model.Scheme;
import com.example.demo.model.SchemeApplication;
import com.example.demo.model.UserDocument;
import com.example.demo.repository.ApplicationDocumentRepository;
import com.example.demo.repository.DocumentRepository;
import com.example.demo.repository.SchemeApplicationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Service
public class SchemeApplicationService {

    @Autowired
    private SchemeApplicationRepository schemeApplicationRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private ApplicationDocumentRepository applicationDocumentRepository;

    public List<SchemeApplication> getAllApplications() {
        return schemeApplicationRepository.findAll();
    }

    public List<SchemeApplication> getApplicationsByUser(GeneralUser user) {
        // Get all applications for the user
        List<SchemeApplication> applications = schemeApplicationRepository.findByUserOrderByApplicationDateDesc(user);

        // Filter out applications with broken bonds
        return applications.stream()
                .filter(app -> app.getBondBroken() == null || !app.getBondBroken())
                .toList();
    }

    /**
     * Get all applications for a user, including those with broken bonds
     */
    public List<SchemeApplication> getAllApplicationsByUser(GeneralUser user) {
        return schemeApplicationRepository.findByUserOrderByApplicationDateDesc(user);
    }

    public List<SchemeApplication> getApplicationsByScheme(Scheme scheme) {
        return schemeApplicationRepository.findByScheme(scheme);
    }

    public List<SchemeApplication> getApplicationsByStatus(String status) {
        return schemeApplicationRepository.findByStatus(status);
    }

    public Optional<SchemeApplication> getApplicationById(Long id) {
        return schemeApplicationRepository.findById(id);
    }

    @Transactional
    public SchemeApplication applyForScheme(GeneralUser user, Scheme scheme, MultipartFile document) throws IOException {
        // Allow multiple applications for the same scheme
        // No need to check for existing applications

        SchemeApplication application = new SchemeApplication(user, scheme);

        if (document != null && !document.isEmpty()) {
            application.setSupportingDocument(document.getBytes());
            application.setDocumentContentType(document.getContentType());
        }

        // Set initial payment status if payment is required
        if (scheme.getPaymentAmount() != null) {
            application.setPaymentStatus("PENDING");
        }

        return schemeApplicationRepository.save(application);
    }

    @Transactional
    public SchemeApplication applyForSchemeWithDocuments(GeneralUser user, Scheme scheme,
                                                        MultipartFile consolidatedDocument,
                                                        List<MultipartFile> documentFiles,
                                                        List<Long> documentIds) throws IOException {
        // Allow multiple applications for the same scheme
        // No need to check for existing applications

        SchemeApplication application = new SchemeApplication(user, scheme);

        // Save the consolidated document if provided (for backward compatibility)
        if (consolidatedDocument != null && !consolidatedDocument.isEmpty()) {
            application.setSupportingDocument(consolidatedDocument.getBytes());
            application.setDocumentContentType(consolidatedDocument.getContentType());
        }

        // Set initial payment status if payment is required
        if (scheme.getPaymentAmount() != null) {
            application.setPaymentStatus("PENDING");
        }

        // Save the application first to get an ID
        application = schemeApplicationRepository.save(application);

        // Process individual document uploads if provided
        if (documentFiles != null && documentIds != null && documentFiles.size() == documentIds.size()) {
            for (int i = 0; i < documentFiles.size(); i++) {
                MultipartFile file = documentFiles.get(i);
                Long documentId = documentIds.get(i);

                if (file != null && !file.isEmpty() && documentId != null) {
                    Optional<Document> documentOpt = documentRepository.findById(documentId);
                    if (documentOpt.isPresent()) {
                        Document document = documentOpt.get();
                        application.addDocument(document, file.getBytes(), file.getContentType(), file.getOriginalFilename());
                    }
                }
            }
            // Save again with document uploads
            application = schemeApplicationRepository.save(application);
        }

        return application;
    }

    @Transactional
    public SchemeApplication applyForSchemeWithPreUploadedDocuments(GeneralUser user, Scheme scheme,
                                                                  MultipartFile consolidatedDocument,
                                                                  List<MultipartFile> documentFiles,
                                                                  List<Long> documentIds,
                                                                  List<Long> preUploadedDocumentIds) throws IOException {
        // Allow multiple applications for the same scheme
        // No need to check for existing applications

        SchemeApplication application = new SchemeApplication(user, scheme);

        // Save the consolidated document if provided (for backward compatibility)
        if (consolidatedDocument != null && !consolidatedDocument.isEmpty()) {
            application.setSupportingDocument(consolidatedDocument.getBytes());
            application.setDocumentContentType(consolidatedDocument.getContentType());
        }

        // Set initial payment status if payment is required
        if (scheme.getPaymentAmount() != null) {
            application.setPaymentStatus("PENDING");
        }

        // Save the application first to get an ID
        application = schemeApplicationRepository.save(application);

        // Process individual document uploads and pre-uploaded documents
        if (documentIds != null && !documentIds.isEmpty()) {
            for (int i = 0; i < documentIds.size(); i++) {
                Long documentId = documentIds.get(i);

                // Check if this document should use a pre-uploaded document
                boolean usePreUploaded = preUploadedDocumentIds != null &&
                                        preUploadedDocumentIds.contains(documentId);

                if (usePreUploaded) {
                    // Use pre-uploaded document
                    Optional<Document> documentOpt = documentRepository.findById(documentId);
                    if (documentOpt.isPresent()) {
                        Document document = documentOpt.get();

                        // Get the user's document of this type
                        Optional<UserDocument> userDocOpt = userDocumentService.getUserDocumentByUserAndDocument(user, document);
                        if (userDocOpt.isPresent()) {
                            UserDocument userDoc = userDocOpt.get();

                            // Add the document to the application
                            application.addDocument(document,
                                                  userDoc.getDocumentFile(),
                                                  userDoc.getDocumentContentType(),
                                                  userDoc.getDocumentName());

                            System.out.println("Using pre-uploaded document: " + userDoc.getDocumentName() +
                                             " for document type: " + document.getName());
                        }
                    }
                } else if (documentFiles != null && i < documentFiles.size()) {
                    // Use uploaded file
                    MultipartFile file = documentFiles.get(i);

                    if (file != null && !file.isEmpty() && documentId != null) {
                        Optional<Document> documentOpt = documentRepository.findById(documentId);
                        if (documentOpt.isPresent()) {
                            Document document = documentOpt.get();
                            application.addDocument(document, file.getBytes(), file.getContentType(), file.getOriginalFilename());
                        }
                    }
                }
            }

            // Save again with document uploads
            application = schemeApplicationRepository.save(application);
        }

        return application;
    }

    @Transactional
    public List<ApplicationDocument> getApplicationDocuments(Long applicationId) {
        Optional<SchemeApplication> applicationOpt = schemeApplicationRepository.findById(applicationId);
        if (applicationOpt.isPresent()) {
            return applicationDocumentRepository.findByApplication(applicationOpt.get());
        }
        return List.of();
    }

    @Transactional
    public ApplicationDocument getApplicationDocument(Long applicationId, Long documentId) {
        Optional<SchemeApplication> applicationOpt = schemeApplicationRepository.findById(applicationId);
        Optional<Document> documentOpt = documentRepository.findById(documentId);

        if (applicationOpt.isPresent() && documentOpt.isPresent()) {
            ApplicationDocument appDoc = applicationDocumentRepository.findByApplicationAndDocument(
                    applicationOpt.get(), documentOpt.get()).orElse(null);

            // Debug log
            if (appDoc != null) {
                System.out.println("Found application document: " + appDoc.getId() +
                                  ", Document name: " + appDoc.getDocumentName() +
                                  ", Has file: " + (appDoc.getDocumentFile() != null));
            } else {
                System.out.println("No application document found for application " + applicationId +
                                  " and document " + documentId);
            }

            return appDoc;
        }
        return null;
    }

    public SchemeApplication updateApplicationStatus(Long id, String status, String remarks) {
        Optional<SchemeApplication> applicationOpt = schemeApplicationRepository.findById(id);

        if (applicationOpt.isPresent()) {
            SchemeApplication application = applicationOpt.get();
            application.setStatus(status);
            application.setRemarks(remarks);
            return schemeApplicationRepository.save(application);
        }

        throw new RuntimeException("Application not found with ID: " + id);
    }

    public void deleteApplication(Long id) {
        schemeApplicationRepository.deleteById(id);
    }

    @Transactional
    public SchemeApplication saveApplication(SchemeApplication application) {
        return schemeApplicationRepository.save(application);
    }

    @Transactional
    public SchemeApplication updatePaymentStatus(Long id, String paymentStatus, String transactionId) {
        Optional<SchemeApplication> applicationOpt = schemeApplicationRepository.findById(id);

        if (applicationOpt.isPresent()) {
            SchemeApplication application = applicationOpt.get();
            application.setPaymentStatus(paymentStatus);
            application.setTransactionId(transactionId);
            return schemeApplicationRepository.save(application);
        }

        throw new RuntimeException("Application not found with ID: " + id);
    }

    @Transactional
    public SchemeApplication updateApplication(SchemeApplication application) {
        if (application.getId() == null) {
            throw new RuntimeException("Cannot update application without ID");
        }

        return schemeApplicationRepository.save(application);
    }

    public long countAllApplications() {
        return schemeApplicationRepository.count();
    }

    public long countApplicationsByStatus(String status) {
        return schemeApplicationRepository.countByStatus(status);
    }

    /**
     * Get all applications that are approved but not yet assigned to any NetCafe
     */
    @Transactional
    public List<SchemeApplication> getAvailableApplicationsForNetCafe() {
        List<SchemeApplication> approvedApplications = schemeApplicationRepository.findByStatus("APPROVED");

        // Filter out applications that are already assigned to a NetCafe
        return approvedApplications.stream()
                .filter(app -> !netCafeApplicationService.isApplicationAssigned(app.getId()))
                .toList();
    }

    @Autowired
    private NetCafeApplicationService netCafeApplicationService;

    @Autowired
    private UserDocumentService userDocumentService;
}
