<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm Application Completion</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #28a745;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .receipt {
            background-color: #fff;
            border: 1px dashed #ccc;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 10px;
        }
        .receipt-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .receipt-body {
            margin-bottom: 20px;
        }
        .receipt-footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #eee;
            font-size: 0.9rem;
        }
        .receipt-id {
            font-family: monospace;
            font-size: 1.2rem;
            font-weight: bold;
            color: #28a745;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Available Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/user/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/payments}">
                            <i class="bi bi-credit-card"></i> Payment History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/application-history}">
                            <i class="bi bi-clock-history"></i> Application History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Confirm Application Completion</h2>
                    <div>
                        <a th:href="@{/user/applications}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Applications
                        </a>
                    </div>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>
                <div class="alert alert-info" role="alert" th:if="${info}" th:text="${info}"></div>

                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-check-circle"></i> Application Completion Receipt
                    </div>
                    <div class="card-body">
                        <div class="receipt">
                            <div class="receipt-header">
                                <h3>Application Completion Receipt</h3>
                                <p>Receipt ID: <span class="receipt-id" th:text="${history.completionReceiptId}">RCPT-12345678</span></p>
                            </div>
                            <div class="receipt-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <p><strong>Scheme:</strong> <span th:text="${history.schemeName}">Scheme Name</span></p>
                                        <p><strong>Application ID:</strong> <span th:text="${history.schemeApplicationId}">123</span></p>
                                        <p><strong>Application Date:</strong> <span th:text="${#temporals.format(history.applicationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Applicant:</strong> <span th:text="${history.userName}">User Name</span></p>
                                        <p><strong>NetCafe:</strong> <span th:text="${history.netCafeName != null ? history.netCafeName : 'N/A'}">NetCafe Name</span></p>
                                        <p><strong>Completion Date:</strong> <span th:text="${history.completionDate != null ? #temporals.format(history.completionDate, 'dd-MM-yyyy HH:mm') : 'Not completed'}">01-01-2023</span></p>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <p><strong>Final Status:</strong> <span th:text="${history.finalStatus}">COMPLETED</span></p>
                                        <p><strong>Remarks:</strong> <span th:text="${history.remarks != null ? history.remarks : 'No remarks'}">Application processed successfully</span></p>
                                    </div>
                                </div>
                                <div class="row mt-3" th:if="${history.paymentAmount != null}">
                                    <div class="col-12">
                                        <h5>Payment Information</h5>
                                        <p><strong>Payment Amount:</strong> ₹<span th:text="${history.paymentAmount}">500.00</span></p>
                                        <p><strong>Transaction ID:</strong> <span th:text="${history.transactionId != null ? history.transactionId : 'N/A'}">TXN123456</span></p>
                                    </div>
                                </div>
                            </div>
                            <div class="receipt-footer">
                                <p>This receipt confirms that your application has been processed and completed by the NetCafe.</p>
                                <p>Please confirm the completion to finalize the process.</p>
                            </div>
                        </div>

                        <form th:action="@{/user/application/confirm-completion}" method="post" class="mt-4">
                            <input type="hidden" name="receiptId" th:value="${history.completionReceiptId}">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="confirmCheck" required>
                                <label class="form-check-label" for="confirmCheck">
                                    I confirm that the application has been completed to my satisfaction
                                </label>
                            </div>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle"></i> Confirm Completion
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
