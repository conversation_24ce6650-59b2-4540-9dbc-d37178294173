<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Unsettled Payments</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #495057;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .summary-card {
            background-color: #ffc107;
            color: #212529;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>NetCafe Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/edit-profile}">
                            <i class="bi bi-person"></i> Edit Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/available-applications}">
                            <i class="bi bi-file-earmark-plus"></i> Available Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/netcafe/payments}">
                            <i class="bi bi-credit-card"></i> Payment History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Unsettled Payments</h2>
                    <a th:href="@{/netcafe/payments}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Payment History
                    </a>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <!-- Payment Summary -->
                <div class="summary-card">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>Unsettled Payment Summary</h4>
                            <p class="fs-5">Total Amount: ₹<span th:text="${totalAmount}">0.00</span></p>
                            <p>These payments will be settled by the admin on a weekly basis.</p>
                        </div>
                        <div class="col-md-6 text-end">
                            <p>Settlement Status: <span class="badge bg-info">Pending</span></p>
                            <p>Expected Settlement: Within 7 days</p>
                        </div>
                    </div>
                </div>

                <!-- Unsettled Payments Table -->
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-cash-stack"></i> Unsettled Commission Payments
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Scheme</th>
                                        <th>Application ID</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:if="${payments.empty}">
                                        <td colspan="5" class="text-center">No unsettled payments found</td>
                                    </tr>
                                    <tr th:each="payment : ${payments}">
                                        <td th:text="${#temporals.format(payment.paymentDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</td>
                                        <td th:text="${payment.application != null && payment.application.scheme != null ? payment.application.scheme.name : 'N/A'}">Scheme Name</td>
                                        <td>
                                            <a th:if="${payment.application != null}" 
                                               th:href="@{/netcafe/application/{id}(id=${payment.netCafeApplication.id})}" 
                                               th:text="${payment.application.id}">
                                                1
                                            </a>
                                            <span th:unless="${payment.application != null}">N/A</span>
                                        </td>
                                        <td>₹<span th:text="${payment.amount}">500.00</span></td>
                                        <td>
                                            <span class="badge bg-warning text-dark">Pending Settlement</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Settlement Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <i class="bi bi-info-circle"></i> Settlement Information
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Settlement Process</h5>
                                <ul>
                                    <li>Commissions are settled on a weekly basis</li>
                                    <li>The admin will process all pending payments</li>
                                    <li>You will receive a notification when payment is processed</li>
                                    <li>Payment will be made to your registered bank account</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5>Commission Rates</h5>
                                <p>Commission rates are set by the admin for each scheme. The rates may be:</p>
                                <ul>
                                    <li>Fixed amount per application</li>
                                    <li>Percentage of the application fee</li>
                                </ul>
                                <p>For more information, please contact the admin.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
