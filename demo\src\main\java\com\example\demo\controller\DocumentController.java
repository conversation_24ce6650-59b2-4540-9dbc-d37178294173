package com.example.demo.controller;

import com.example.demo.model.NetCafeUser;
import com.example.demo.service.NetCafeUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Optional;

@Controller
@RequestMapping("/documents")
public class DocumentController {

    @Autowired
    private NetCafeUserService netCafeUserService;

    @GetMapping("/photo/{userId}")
    public ResponseEntity<byte[]> getUserPhoto(@PathVariable Long userId) {
        Optional<NetCafeUser> userOpt = netCafeUserService.findById(userId);
        
        if (userOpt.isPresent()) {
            NetCafeUser user = userOpt.get();
            byte[] photoData = user.getPhoto();
            
            if (photoData != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType(user.getPhotoContentType()));
                return new ResponseEntity<>(photoData, headers, HttpStatus.OK);
            }
        }
        
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @GetMapping("/csc-certificate/{userId}")
    public ResponseEntity<byte[]> getCscCertificate(@PathVariable Long userId) {
        Optional<NetCafeUser> userOpt = netCafeUserService.findById(userId);
        
        if (userOpt.isPresent()) {
            NetCafeUser user = userOpt.get();
            byte[] certificateData = user.getCscCertificate();
            
            if (certificateData != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType(user.getCscCertificateContentType()));
                return new ResponseEntity<>(certificateData, headers, HttpStatus.OK);
            }
        }
        
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @GetMapping("/aadhar-card/{userId}")
    public ResponseEntity<byte[]> getAadharCard(@PathVariable Long userId) {
        Optional<NetCafeUser> userOpt = netCafeUserService.findById(userId);
        
        if (userOpt.isPresent()) {
            NetCafeUser user = userOpt.get();
            byte[] aadharData = user.getAadharCardPhoto();
            
            if (aadharData != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType(user.getAadharCardPhotoContentType()));
                return new ResponseEntity<>(aadharData, headers, HttpStatus.OK);
            }
        }
        
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @GetMapping("/pan-card/{userId}")
    public ResponseEntity<byte[]> getPanCard(@PathVariable Long userId) {
        Optional<NetCafeUser> userOpt = netCafeUserService.findById(userId);
        
        if (userOpt.isPresent()) {
            NetCafeUser user = userOpt.get();
            byte[] panData = user.getPanCardPhoto();
            
            if (panData != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType(user.getPanCardPhotoContentType()));
                return new ResponseEntity<>(panData, headers, HttpStatus.OK);
            }
        }
        
        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }
}
