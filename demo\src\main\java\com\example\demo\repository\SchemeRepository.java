package com.example.demo.repository;

import com.example.demo.model.Scheme;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SchemeRepository extends JpaRepository<Scheme, Long> {
    
    List<Scheme> findByActiveTrue();
    
    List<Scheme> findByActiveTrueOrderByCreationDateDesc();
}
