package com.example.demo.model;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "payment_records")
public class PaymentRecord {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "scheme_application_id")
    private SchemeApplication application;

    @ManyToOne
    @JoinColumn(name = "netcafe_application_id")
    private NetCafeApplication netCafeApplication;

    @ManyToOne
    @JoinColumn(name = "user_id")
    private GeneralUser user;

    @ManyToOne
    @JoinColumn(name = "netcafe_user_id")
    private NetCafeUser netCafeUser;

    @Column(name = "payment_type")
    private String paymentType; // USER_PAYMENT, NETCAFE_COMMISSION, ADMIN_TO_NETCAFE

    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "transaction_id")
    private String transactionId;

    @Column(name = "payment_date")
    private LocalDateTime paymentDate;

    @Column(name = "status")
    private String status; // PENDING, COMPLETED, FAILED

    @Column(name = "payment_method")
    private String paymentMethod; // UPI, BANK_TRANSFER, CASH, etc.

    @Column(name = "remarks", length = 1000)
    private String remarks;

    @Column(name = "is_settled")
    private Boolean isSettled;

    @Column(name = "settlement_date")
    private LocalDateTime settlementDate;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Constructors
    public PaymentRecord() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.isSettled = false;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public SchemeApplication getApplication() {
        return application;
    }

    public void setApplication(SchemeApplication application) {
        this.application = application;
    }

    public NetCafeApplication getNetCafeApplication() {
        return netCafeApplication;
    }

    public void setNetCafeApplication(NetCafeApplication netCafeApplication) {
        this.netCafeApplication = netCafeApplication;
    }

    public GeneralUser getUser() {
        return user;
    }

    public void setUser(GeneralUser user) {
        this.user = user;
    }

    public NetCafeUser getNetCafeUser() {
        return netCafeUser;
    }

    public void setNetCafeUser(NetCafeUser netCafeUser) {
        this.netCafeUser = netCafeUser;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public LocalDateTime getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDateTime paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public Boolean getIsSettled() {
        return isSettled;
    }

    public void setIsSettled(Boolean isSettled) {
        this.isSettled = isSettled;
    }

    public LocalDateTime getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(LocalDateTime settlementDate) {
        this.settlementDate = settlementDate;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
}
