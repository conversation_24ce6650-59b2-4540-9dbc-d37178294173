package com.example.demo.controller;

import com.example.demo.model.Admin;
import com.example.demo.model.GeneralUser;
import com.example.demo.model.NetCafeApplication;
import com.example.demo.model.NetCafeUser;
import com.example.demo.model.SchemeApplication;
import com.example.demo.service.ApplicationHistoryService;
import com.example.demo.service.NetCafeApplicationService;
import com.example.demo.service.SchemeApplicationService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.servlet.http.HttpSession;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Controller
public class BondController {

    @Autowired
    private SchemeApplicationService schemeApplicationService;

    @Autowired
    private NetCafeApplicationService netCafeApplicationService;

    @Autowired
    private ApplicationHistoryService applicationHistoryService;

    // Admin Bond Management
    @GetMapping("/admin/bonds")
    public String adminBondManagement(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        List<SchemeApplication> applications = schemeApplicationService.getAllApplications();
        model.addAttribute("applications", applications);
        model.addAttribute("admin", admin);

        return "admin/bonds/index";
    }

    @GetMapping("/admin/bonds/{id}/break-form")
    public String adminBreakBondForm(@PathVariable Long id, HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        Admin admin = (Admin) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);
            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/admin/bonds";
            }

            SchemeApplication application = applicationOpt.get();

            // Check if application is assigned to a NetCafe
            List<NetCafeApplication> netCafeApplications = netCafeApplicationService.getApplicationsBySchemeApplicationId(id);
            if (netCafeApplications.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "This application is not assigned to any NetCafe");
                return "redirect:/admin/bonds";
            }

            // Check if bond is already broken
            if (application.getBondBroken() != null && application.getBondBroken()) {
                redirectAttributes.addFlashAttribute("error", "Bond is already broken for this application");
                return "redirect:/admin/bonds";
            }

            model.addAttribute("app", application);
            model.addAttribute("netCafeApp", netCafeApplications.get(0));
            model.addAttribute("admin", admin);

            return "admin/bonds/break-form";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error loading break bond form: " + e.getMessage());
            return "redirect:/admin/bonds";
        }
    }

    @PostMapping("/admin/bonds/{id}/break")
    public String adminBreakBond(
            @PathVariable Long id,
            @RequestParam String reason,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);
            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/admin/bonds";
            }

            SchemeApplication application = applicationOpt.get();

            // Find the NetCafe application
            List<NetCafeApplication> netCafeApplications = netCafeApplicationService.getApplicationsBySchemeApplicationId(id);
            if (netCafeApplications.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "No NetCafe application found for this scheme application");
                return "redirect:/admin/bonds";
            }

            NetCafeApplication netCafeApplication = netCafeApplications.get(0);

            // Break the bond
            netCafeApplication.setBondBroken(true);
            netCafeApplication.setBondBreakReason(reason);
            netCafeApplication.setBondBrokenDate(LocalDateTime.now());
            netCafeApplication.setBondBreakInitiator("ADMIN");
            netCafeApplicationService.saveApplication(netCafeApplication);

            // Update the scheme application
            application.setBondBroken(true);
            application.setBondBreakReason(reason);
            application.setBondBrokenDate(LocalDateTime.now());
            application.setStatus("BOND_BROKEN");
            schemeApplicationService.saveApplication(application);

            // Create application history
            applicationHistoryService.createApplicationHistory(application, netCafeApplication);

            redirectAttributes.addFlashAttribute("success", "Bond broken successfully");
            return "redirect:/admin/bonds";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error breaking bond: " + e.getMessage());
            return "redirect:/admin/bonds";
        }
    }

    // User Bond Management
    @GetMapping("/user/bonds")
    public String userBondManagement(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        if (user == null) {
            return "redirect:/user/login";
        }

        List<SchemeApplication> applications = schemeApplicationService.getAllApplicationsByUser(user);
        model.addAttribute("applications", applications);
        model.addAttribute("user", user);

        return "user/bonds/index";
    }

    // NetCafe Bond Management
    @GetMapping("/netcafe/bonds")
    public String netcafeBondManagement(HttpSession session, Model model) {
        NetCafeUser netCafeUser = (NetCafeUser) session.getAttribute("netCafeUser");
        if (netCafeUser == null) {
            return "redirect:/netcafe/login";
        }

        List<NetCafeApplication> applications = netCafeApplicationService.getAllApplicationsByNetCafeUser(netCafeUser);
        model.addAttribute("applications", applications);
        model.addAttribute("netCafeUser", netCafeUser);

        return "netcafe/bonds/index";
    }
}
