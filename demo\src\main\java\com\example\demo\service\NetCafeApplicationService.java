package com.example.demo.service;

import com.example.demo.model.NetCafeApplication;
import com.example.demo.model.NetCafeUser;
import com.example.demo.model.SchemeApplication;
import com.example.demo.repository.NetCafeApplicationRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class NetCafeApplicationService {

    @Autowired
    private NetCafeApplicationRepository netCafeApplicationRepository;

    @Autowired
    private SchemeApplicationService schemeApplicationService;

    /**
     * Assign a scheme application to a NetCafe user
     */
    @Transactional
    public NetCafeApplication assignApplication(NetCafeUser netCafeUser, SchemeApplication application) {
        // Check if application is already assigned
        if (netCafeApplicationRepository.existsByApplication_Id(application.getId())) {
            throw new RuntimeException("Application is already assigned to a NetCafe");
        }

        // Automatically grant document access when application is assigned to NetCafe
        application.setDocumentAccessGranted(true);
        application.setDocumentAccessGrantedDate(LocalDateTime.now());
        schemeApplicationService.updateApplication(application);

        NetCafeApplication netCafeApplication = new NetCafeApplication(netCafeUser, application);
        return netCafeApplicationRepository.save(netCafeApplication);
    }

    /**
     * Get all applications assigned to a NetCafe user
     */
    public List<NetCafeApplication> getApplicationsByNetCafeUser(NetCafeUser netCafeUser) {
        // Get all applications for the NetCafe user
        List<NetCafeApplication> applications = netCafeApplicationRepository.findByNetCafeUser(netCafeUser);

        // Filter out applications with broken bonds
        return applications.stream()
                .filter(app -> app.getBondBroken() == null || !app.getBondBroken())
                .toList();
    }

    /**
     * Get all applications for a NetCafe user, including those with broken bonds
     */
    public List<NetCafeApplication> getAllApplicationsByNetCafeUser(NetCafeUser netCafeUser) {
        return netCafeApplicationRepository.findByNetCafeUser(netCafeUser);
    }

    /**
     * Get all pending applications assigned to a NetCafe user
     */
    public List<NetCafeApplication> getPendingApplicationsByNetCafeUser(NetCafeUser netCafeUser) {
        List<NetCafeApplication> applications = netCafeApplicationRepository.findByNetCafeUserAndStatus(netCafeUser, "PENDING");

        // Filter out applications with broken bonds
        return applications.stream()
                .filter(app -> app.getBondBroken() == null || !app.getBondBroken())
                .toList();
    }

    /**
     * Get all processing applications assigned to a NetCafe user
     */
    public List<NetCafeApplication> getProcessingApplicationsByNetCafeUser(NetCafeUser netCafeUser) {
        List<NetCafeApplication> applications = netCafeApplicationRepository.findByNetCafeUserAndStatus(netCafeUser, "PROCESSING");

        // Filter out applications with broken bonds
        return applications.stream()
                .filter(app -> app.getBondBroken() == null || !app.getBondBroken())
                .toList();
    }

    /**
     * Get all completed applications assigned to a NetCafe user
     */
    public List<NetCafeApplication> getCompletedApplicationsByNetCafeUser(NetCafeUser netCafeUser) {
        List<NetCafeApplication> applications = netCafeApplicationRepository.findByNetCafeUserAndStatus(netCafeUser, "COMPLETED");

        // Filter out applications with broken bonds
        return applications.stream()
                .filter(app -> app.getBondBroken() == null || !app.getBondBroken())
                .toList();
    }

    /**
     * Get application by ID
     */
    public Optional<NetCafeApplication> getApplicationById(Long id) {
        return netCafeApplicationRepository.findById(id);
    }

    /**
     * Get NetCafe application by scheme application ID
     * Returns the most recent NetCafeApplication for a SchemeApplication
     */
    public Optional<NetCafeApplication> getBySchemeApplicationId(Long applicationId) {
        List<NetCafeApplication> applications = netCafeApplicationRepository.findByApplicationIdOrderByIdDesc(applicationId);
        if (applications.isEmpty()) {
            return Optional.empty();
        }
        // Return the most recent application (first in the list due to DESC ordering)
        return Optional.of(applications.get(0));
    }

    /**
     * Update application status
     */
    @Transactional
    public NetCafeApplication updateStatus(Long id, String status, String remarks) {
        Optional<NetCafeApplication> applicationOpt = netCafeApplicationRepository.findById(id);

        if (applicationOpt.isEmpty()) {
            throw new RuntimeException("NetCafe application not found with ID: " + id);
        }

        NetCafeApplication application = applicationOpt.get();
        application.setStatus(status);
        application.setRemarks(remarks);

        if (status.equals("COMPLETED")) {
            application.setCompletionDate(LocalDateTime.now());
        }

        return netCafeApplicationRepository.save(application);
    }

    /**
     * Check if a scheme application is assigned to any NetCafe
     */
    public boolean isApplicationAssigned(Long applicationId) {
        return netCafeApplicationRepository.existsByApplication_Id(applicationId);
    }

    /**
     * Download all documents for a scheme application
     * This method will be implemented to create a ZIP file with all documents
     */
    public byte[] downloadAllDocuments(Long netCafeApplicationId) {
        // Implementation will be added later
        return null;
    }

    /**
     * Get all available applications that are not assigned to any NetCafe
     */
    public List<SchemeApplication> getAvailableApplications() {
        // Get all applications with status PENDING
        List<SchemeApplication> pendingApplications = schemeApplicationService.getApplicationsByStatus("PENDING");

        // Filter out applications that are already assigned to a NetCafe
        return pendingApplications.stream()
                .filter(app -> !isApplicationAssigned(app.getId()))
                .toList();
    }

    /**
     * Claim an application for a NetCafe user
     */
    @Transactional
    public NetCafeApplication claimApplication(NetCafeUser netCafeUser, Long applicationId) {
        // Check if application exists
        Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(applicationId);

        if (applicationOpt.isEmpty()) {
            throw new RuntimeException("Application not found with ID: " + applicationId);
        }

        SchemeApplication application = applicationOpt.get();

        // Check if application is already assigned
        if (isApplicationAssigned(applicationId)) {
            throw new RuntimeException("Application is already assigned to a NetCafe");
        }

        // Check if application is in PENDING status
        if (!"PENDING".equals(application.getStatus())) {
            throw new RuntimeException("Only pending applications can be claimed");
        }

        // Automatically grant document access when NetCafe claims the application
        application.setDocumentAccessGranted(true);
        application.setDocumentAccessGrantedDate(LocalDateTime.now());
        schemeApplicationService.updateApplication(application);

        // Create new NetCafeApplication
        NetCafeApplication netCafeApplication = new NetCafeApplication(netCafeUser, application);

        // Save and return
        return netCafeApplicationRepository.save(netCafeApplication);
    }

    /**
     * Save a NetCafe application
     */
    @Transactional
    public NetCafeApplication saveApplication(NetCafeApplication netCafeApplication) {
        return netCafeApplicationRepository.save(netCafeApplication);
    }

    /**
     * Count all NetCafe applications
     */
    public long countAllApplications() {
        return netCafeApplicationRepository.count();
    }

    /**
     * Count completed NetCafe applications
     */
    public long countCompletedApplications() {
        return netCafeApplicationRepository.countByStatus("COMPLETED");
    }

    /**
     * Break bond between user and NetCafe
     */
    @Transactional
    public NetCafeApplication breakBond(Long netCafeApplicationId) {
        Optional<NetCafeApplication> applicationOpt = netCafeApplicationRepository.findById(netCafeApplicationId);

        if (applicationOpt.isEmpty()) {
            throw new RuntimeException("NetCafe application not found with ID: " + netCafeApplicationId);
        }

        NetCafeApplication application = applicationOpt.get();
        application.setBondBroken(true);
        application.setBondBrokenDate(LocalDateTime.now());

        // Also update the scheme application if it exists
        if (application.getApplication() != null) {
            SchemeApplication schemeApp = application.getApplication();
            schemeApp.setBondBroken(true);
            schemeApplicationService.saveApplication(schemeApp);
        }

        return netCafeApplicationRepository.save(application);
    }

    /**
     * Get applications by scheme application ID
     */
    public List<NetCafeApplication> getApplicationsBySchemeApplicationId(Long applicationId) {
        return netCafeApplicationRepository.findByApplicationIdOrderByIdDesc(applicationId);
    }

    /**
     * Get all NetCafe applications
     */
    public List<NetCafeApplication> getAllApplications() {
        return netCafeApplicationRepository.findAll();
    }
}
