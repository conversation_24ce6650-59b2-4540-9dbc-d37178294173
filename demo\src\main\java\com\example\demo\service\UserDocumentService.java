package com.example.demo.service;

import com.example.demo.model.Document;
import com.example.demo.model.GeneralUser;
import com.example.demo.model.UserDocument;
import com.example.demo.repository.DocumentRepository;
import com.example.demo.repository.UserDocumentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class UserDocumentService {

    @Autowired
    private UserDocumentRepository userDocumentRepository;

    @Autowired
    private DocumentRepository documentRepository;

    /**
     * Get all documents for a user
     */
    public List<UserDocument> getUserDocuments(GeneralUser user) {
        return userDocumentRepository.findByUserOrderByUploadDateDesc(user);
    }

    /**
     * Get a specific user document by ID
     */
    public Optional<UserDocument> getUserDocumentById(Long id) {
        return userDocumentRepository.findById(id);
    }

    /**
     * Get a specific user document by user and document type
     */
    public Optional<UserDocument> getUserDocumentByUserAndDocument(GeneralUser user, Document document) {
        return userDocumentRepository.findByUserAndDocument(user, document);
    }

    /**
     * Save a new user document
     */
    @Transactional
    public UserDocument saveUserDocument(GeneralUser user, Long documentId, MultipartFile file, String description) throws IOException {
        Optional<Document> documentOpt = documentRepository.findById(documentId);
        
        if (documentOpt.isEmpty()) {
            throw new RuntimeException("Document type not found");
        }
        
        Document document = documentOpt.get();
        
        // Check if user already has this document type
        Optional<UserDocument> existingDocOpt = userDocumentRepository.findByUserAndDocument(user, document);
        
        UserDocument userDocument;
        
        if (existingDocOpt.isPresent()) {
            // Update existing document
            userDocument = existingDocOpt.get();
            userDocument.setDocumentFile(file.getBytes());
            userDocument.setDocumentContentType(file.getContentType());
            userDocument.setDocumentName(file.getOriginalFilename());
            userDocument.setUploadDate(LocalDateTime.now());
            userDocument.setDescription(description);
            userDocument.setIsVerified(false);
            userDocument.setVerificationDate(null);
            userDocument.setVerificationRemarks(null);
        } else {
            // Create new document
            userDocument = new UserDocument(
                user,
                document,
                file.getBytes(),
                file.getContentType(),
                file.getOriginalFilename()
            );
            userDocument.setDescription(description);
        }
        
        return userDocumentRepository.save(userDocument);
    }

    /**
     * Delete a user document
     */
    @Transactional
    public void deleteUserDocument(Long id) {
        userDocumentRepository.deleteById(id);
    }

    /**
     * Verify a user document (admin function)
     */
    @Transactional
    public UserDocument verifyUserDocument(Long id, Boolean isVerified, String remarks) {
        Optional<UserDocument> userDocumentOpt = userDocumentRepository.findById(id);
        
        if (userDocumentOpt.isEmpty()) {
            throw new RuntimeException("User document not found");
        }
        
        UserDocument userDocument = userDocumentOpt.get();
        userDocument.setIsVerified(isVerified);
        userDocument.setVerificationDate(LocalDateTime.now());
        userDocument.setVerificationRemarks(remarks);
        
        return userDocumentRepository.save(userDocument);
    }

    /**
     * Get all unverified documents (admin function)
     */
    public List<UserDocument> getUnverifiedDocuments() {
        return userDocumentRepository.findByIsVerified(false);
    }

    /**
     * Count user documents
     */
    public long countUserDocuments(GeneralUser user) {
        return userDocumentRepository.countByUser(user);
    }

    /**
     * Count unverified documents (admin function)
     */
    public long countUnverifiedDocuments() {
        return userDocumentRepository.countByIsVerified(false);
    }
}
