<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NetCafe - Application Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #6f42c1;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .section-title {
            color: #6f42c1;
            border-bottom: 2px solid #6f42c1;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-processing {
            background-color: #17a2b8;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-completed {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-confirmed {
            background-color: #0d6efd;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-bond-broken {
            background-color: #6c757d;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .messages-container {
            max-height: 400px;
            overflow-y: auto;
        }
        .message-bubble {
            max-width: 80%;
            display: inline-block;
            text-align: start;
        }
        .payment-screenshot {
            max-height: 150px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>NetCafe Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/netcafe/applications}">
                            <i class="bi bi-file-earmark-text"></i> Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/application-history}">
                            <i class="bi bi-clock-history"></i> Application History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/broken-bonds}">
                            <i class="bi bi-scissors"></i> Broken Bonds
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/edit-profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2>Application Details</h2>
                    <p>Review and process scheme application</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <!-- Debug Information -->
                <div class="alert alert-info mb-3">
                    <h5>Debug Information</h5>
                    <p><strong>NetCafe User:</strong> <span th:text="${user != null ? user.name : 'Not Present'}">NetCafe User</span></p>
                    <p><strong>NetCafe Application:</strong> <span th:text="${netCafeApplication != null ? netCafeApplication.id : 'Not Present'}">NetCafe App ID</span></p>
                    <p><strong>Scheme Application:</strong> <span th:text="${application != null ? application.id : 'Not Present'}">Scheme App ID</span></p>
                    <p><strong>Applicant User:</strong> <span th:text="${application != null && application.user != null ? application.user.name : 'Not Present'}">Applicant Name</span></p>
                    <p><strong>Scheme:</strong> <span th:text="${application != null && application.scheme != null ? application.scheme.name : 'Not Present'}">Scheme Name</span></p>
                    <p><strong>Document Access:</strong> <span th:text="${hasDocumentAccess}">false</span></p>
                </div>

                <div class="card">
                    <div class="card-header">
                        Application Information
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="section-title">Application Status</h5>
                                <p><strong>Application ID:</strong> <span th:text="${application.id}">1</span></p>
                                <p><strong>Application Date:</strong> <span th:text="${#temporals.format(application.applicationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                <p><strong>Status:</strong> <span th:text="${application.status}">PENDING</span></p>
                                <p th:if="${application.remarks != null && !application.remarks.isEmpty()}">
                                    <strong>Remarks:</strong> <span th:text="${application.remarks}">Remarks</span>
                                </p>
                                <p><strong>NetCafe Status:</strong>
                                    <span th:if="${netCafeApplication.status == 'PENDING'}" class="badge badge-pending">Pending</span>
                                    <span th:if="${netCafeApplication.status == 'PROCESSING'}" class="badge badge-processing">Processing</span>
                                    <span th:if="${netCafeApplication.status == 'COMPLETED'}" class="badge badge-completed">Completed</span>
                                    <span th:if="${netCafeApplication.status == 'CONFIRMED'}" class="badge badge-confirmed">Confirmed</span>
                                    <span th:if="${netCafeApplication.status == 'BOND_BROKEN'}" class="badge badge-bond-broken">Bond Broken</span>
                                </p>
                                <p th:if="${netCafeApplication.remarks != null && !netCafeApplication.remarks.isEmpty()}">
                                    <strong>NetCafe Remarks:</strong> <span th:text="${netCafeApplication.remarks}">Remarks</span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h5 class="section-title">Scheme Information</h5>
                                <div th:if="${application.scheme != null}">
                                    <p><strong>Scheme Name:</strong> <span th:text="${application.scheme.name}">Scheme Name</span></p>
                                    <p><strong>Scheme ID:</strong> <span th:text="${application.scheme.id}">1</span></p>
                                    <p><strong>Description:</strong> <span th:text="${application.scheme.description != null ? #strings.abbreviate(application.scheme.description, 150) : 'No description available'}">Scheme description...</span></p>
                                </div>
                                <div th:if="${application.scheme == null}" class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle"></i> Scheme information not available
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h5 class="section-title">Applicant Information</h5>
                                <div th:if="${application.user != null}">
                                    <p><strong>Name:</strong> <span th:text="${application.user.name}">John Doe</span></p>
                                    <p><strong>Email:</strong> <span th:text="${application.user.email}"><EMAIL></span></p>
                                    <p><strong>Mobile:</strong> <span th:text="${application.user.mobileNumber}">1234567890</span></p>
                                    <p><strong>Address:</strong> <span th:text="${application.user.address}">123 Main St</span></p>
                                </div>
                                <div th:if="${application.user == null}" class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle"></i> User information not available
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5 class="section-title">Required Documents</h5>
                                <div th:if="${requiredDocuments != null && !requiredDocuments.empty}" class="mb-3">
                                    <ul class="list-group">
                                        <li class="list-group-item" th:each="doc : ${requiredDocuments}">
                                            <i class="bi bi-file-earmark-text text-success me-2"></i>
                                            <strong th:text="${doc.name}">Document Name</strong>
                                        </li>
                                    </ul>
                                </div>
                                <div th:if="${requiredDocuments == null || requiredDocuments.empty}" class="alert alert-info">
                                    <i class="bi bi-info-circle"></i> No specific documents were required for this scheme.
                                </div>
                            </div>
                        </div>

                        <!-- Payment Information Section -->
                        <div class="row mt-4" th:if="${application.scheme != null && application.scheme.paymentAmount != null}">
                            <div class="col-md-12">
                                <h5 class="section-title">Payment Information</h5>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>Payment Amount:</strong> ₹<span th:text="${application.scheme.paymentAmount}">500.00</span></p>
                                                <p><strong>Payment Status:</strong>
                                                    <span th:if="${application.paymentStatus == 'PENDING'}" class="badge badge-pending">Pending</span>
                                                    <span th:if="${application.paymentStatus == 'COMPLETED'}" class="badge badge-processing">Completed</span>
                                                    <span th:if="${application.paymentStatus == 'VERIFIED'}" class="badge badge-completed">Verified</span>
                                                </p>
                                                <p th:if="${application.transactionId != null && !application.transactionId.isEmpty()}">
                                                    <strong>Transaction ID:</strong> <span th:text="${application.transactionId}">TXN123456</span>
                                                </p>
                                            </div>
                                            <div class="col-md-6 text-center" th:if="${application.paymentScreenshot != null}">
                                                <p><strong>Payment Screenshot:</strong></p>
                                                <img th:src="@{/admin/schemes/applications/payment-screenshot/{id}(id=${application.id})}"
                                                     alt="Payment Screenshot" class="img-thumbnail payment-screenshot">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h5 class="section-title">Submitted Documents</h5>

                                <!-- Document Access Status -->
                                <div th:if="${hasDocumentAccess}" class="alert alert-success mb-3">
                                    <i class="bi bi-unlock me-2"></i> <strong>Document Access Granted:</strong> Document access was automatically granted when you claimed this application.
                                    <br><small class="text-muted">Granted on: <span th:text="${#temporals.format(netCafeApplication.application.documentAccessGrantedDate, 'dd-MM-yyyy HH:mm')}"></span></small>
                                </div>

                                <!-- This should rarely happen with automatic granting, but kept as fallback -->
                                <div th:if="${!hasDocumentAccess}" class="alert alert-warning mb-3">
                                    <i class="bi bi-shield-lock me-2"></i> <strong>Document Access Pending:</strong> Document access is being processed automatically.
                                </div>

                                <!-- Individual document uploads -->
                                <div th:if="${hasDocumentAccess && application.applicationDocuments != null && !application.applicationDocuments.empty}" class="mb-3">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Document Type</th>
                                                    <th>File Name</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr th:each="appDoc : ${application.applicationDocuments}">
                                                    <td th:text="${appDoc.document != null ? appDoc.document.name : 'Unknown'}">Document Name</td>
                                                    <td th:text="${appDoc.documentName != null ? appDoc.documentName : 'Unknown'}">file.pdf</td>
                                                    <td>
                                                        <a th:if="${appDoc.document != null}"
                                                           th:href="@{/admin/schemes/applications/document/{appId}/{docId}(appId=${application.id},docId=${appDoc.document.id})}"
                                                           class="btn btn-sm btn-outline-primary" target="_blank">
                                                            <i class="bi bi-eye"></i> View
                                                        </a>
                                                        <span th:if="${appDoc.document == null}" class="text-muted">Not available</span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Consolidated document (if any) -->
                                <div th:if="${hasDocumentAccess && application.supportingDocument != null}" class="mt-3">
                                    <h6>Additional Supporting Document</h6>
                                    <a th:href="@{/admin/schemes/applications/document/{id}(id=${application.id})}" class="btn btn-outline-primary" target="_blank">
                                        <i class="bi bi-file-earmark-pdf"></i> View Consolidated Document
                                    </a>
                                </div>

                                <!-- Download all documents button -->
                                <div class="mt-3" th:if="${hasDocumentAccess}">
                                    <a th:href="@{/netcafe/application/{id}/download-documents(id=${netCafeApplication.id})}" class="btn btn-success">
                                        <i class="bi bi-download"></i> Download All Documents
                                    </a>
                                </div>

                                <!-- No documents message -->
                                <div th:if="${hasDocumentAccess && (application.applicationDocuments == null || application.applicationDocuments.empty) && application.supportingDocument == null}" class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle"></i> No documents were submitted with this application.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                Update Application Status
                            </div>
                            <div class="card-body">
                                <form th:action="@{/netcafe/application/{id}/update-status(id=${netCafeApplication.id})}" method="post">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status" required>
                                            <option value="PENDING" th:selected="${netCafeApplication.status == 'PENDING'}">Pending</option>
                                            <option value="PROCESSING" th:selected="${netCafeApplication.status == 'PROCESSING'}">Processing</option>
                                            <option value="COMPLETED" th:selected="${netCafeApplication.status == 'COMPLETED'}">Completed</option>
                                            <option value="CONFIRMED" th:selected="${netCafeApplication.status == 'CONFIRMED'}" disabled>Confirmed (User Confirmed)</option>
                                            <option value="BOND_BROKEN" th:selected="${netCafeApplication.status == 'BOND_BROKEN'}" disabled>Bond Broken</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="remarks" class="form-label">Remarks</label>
                                        <textarea class="form-control" id="remarks" name="remarks" rows="3" th:text="${netCafeApplication.remarks}"></textarea>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-primary">Update Status</button>
                                    </div>
                                </form>

                                <!-- Completion Confirmation Section (only shown for completed applications) -->
                                <div th:if="${netCafeApplication.status == 'COMPLETED' && (netCafeApplication.readyForCompletion == null || !netCafeApplication.readyForCompletion)}" class="mt-4">
                                    <div class="alert alert-success">
                                        <h5><i class="bi bi-check-circle"></i> Mark Ready for User Confirmation</h5>
                                        <p>Once you have completed all work for this application, you can mark it as ready for user confirmation.</p>
                                        <p>This will notify the user to review and confirm completion, which will automatically break the bond.</p>
                                        <form th:action="@{/netcafe/application/{id}/mark-ready-for-completion(id=${netCafeApplication.id})}" method="post" style="display: inline;">
                                            <button type="submit" class="btn btn-success" onclick="return confirm('Are you sure you want to mark this application as ready for user confirmation?')">
                                                <i class="bi bi-check-circle"></i> Mark Ready for User Confirmation
                                            </button>
                                        </form>
                                    </div>
                                </div>

                                <!-- Ready for Completion Status -->
                                <div th:if="${netCafeApplication.readyForCompletion != null && netCafeApplication.readyForCompletion && (netCafeApplication.userConfirmedCompletion == null || !netCafeApplication.userConfirmedCompletion)}" class="mt-4">
                                    <div class="alert alert-info">
                                        <h5><i class="bi bi-clock"></i> Waiting for User Confirmation</h5>
                                        <p>You have marked this application as ready for completion. The user has been notified and is reviewing the work.</p>
                                        <p th:if="${netCafeApplication.readyForCompletionDate != null}">
                                            <strong>Marked Ready On:</strong> <span th:text="${#temporals.format(netCafeApplication.readyForCompletionDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span>
                                        </p>
                                        <p><em>The bond will be automatically broken once the user confirms completion.</em></p>
                                    </div>
                                </div>

                                <!-- User Confirmed Completion -->
                                <div th:if="${netCafeApplication.userConfirmedCompletion != null && netCafeApplication.userConfirmedCompletion}" class="mt-4">
                                    <div class="alert alert-success">
                                        <h5><i class="bi bi-check-circle-fill"></i> User Confirmed Completion</h5>
                                        <p>The user has confirmed completion of this application. The bond has been automatically broken.</p>
                                        <p th:if="${netCafeApplication.userConfirmationDate != null}">
                                            <strong>Confirmed On:</strong> <span th:text="${#temporals.format(netCafeApplication.userConfirmationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span>
                                        </p>
                                    </div>
                                </div>

                                <!-- Bond Breaking Section (only shown for confirmed applications) -->
                                <div th:if="${netCafeApplication.status == 'CONFIRMED'}" class="mt-4">
                                    <div class="alert alert-warning">
                                        <h5><i class="bi bi-exclamation-triangle"></i> Break Bond</h5>
                                        <p>If you need to break the bond with this user, you can do so by clicking the button below.</p>
                                        <p>This action is permanent and cannot be undone.</p>
                                        <a th:href="@{/netcafe/application/break-bond/{receiptId}(receiptId=${applicationHistory != null ? applicationHistory.completionReceiptId : ''})}"
                                           class="btn btn-danger">
                                            <i class="bi bi-scissors"></i> Break Bond
                                        </a>
                                    </div>
                                </div>

                                <!-- Bond Broken Information -->
                                <div th:if="${netCafeApplication.status == 'BOND_BROKEN'}" class="mt-4">
                                    <div class="alert alert-secondary">
                                        <h5><i class="bi bi-scissors"></i> Bond Broken</h5>
                                        <p>The bond with this user has been broken.</p>
                                        <p th:if="${netCafeApplication.bondBrokenDate != null}">
                                            <strong>Date:</strong> <span th:text="${#temporals.format(netCafeApplication.bondBrokenDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span>
                                        </p>
                                        <p th:if="${netCafeApplication.bondBreakReason != null && !netCafeApplication.bondBreakReason.isEmpty()}">
                                            <strong>Reason:</strong> <span th:text="${netCafeApplication.bondBreakReason}">Reason</span>
                                        </p>
                                        <p th:if="${netCafeApplication.bondBreakInitiator != null}">
                                            <strong>Initiated By:</strong> <span th:text="${netCafeApplication.bondBreakInitiator == 'USER' ? 'User' : 'NetCafe'}">Initiator</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Messaging Section -->
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <i class="bi bi-chat-dots me-2"></i> Messages with Applicant
                            </div>
                            <div class="card-body">
                                <!-- Messages Display -->
                                <div class="messages-container mb-4">
                                    <div th:if="${messages != null && !messages.empty}">
                                        <div th:each="message : ${messages}" class="mb-3">
                                            <div th:class="${message.senderType == 'NETCAFE' ? 'text-end' : 'text-start'}">
                                                <div th:class="${message.senderType == 'NETCAFE' ? 'alert alert-primary message-bubble' : 'alert alert-secondary message-bubble'}">
                                                    <div class="mb-1">
                                                        <strong th:text="${message.senderType == 'NETCAFE' ? 'You' : (application.user != null ? application.user.name : 'Applicant')}">Sender</strong>
                                                        <small class="text-muted ms-2" th:text="${#temporals.format(message.sentDate, 'dd-MM-yyyy HH:mm')}">Date</small>
                                                    </div>
                                                    <div th:text="${message.content}">Message content</div>
                                                    <!-- Attachment display -->
                                                    <div th:if="${message.hasAttachment()}" class="mt-2 border-top pt-2">
                                                        <div class="d-flex align-items-center">
                                                            <i class="bi bi-paperclip me-2"></i>
                                                            <span th:if="${message.attachmentType == 'IMAGE'}" class="badge bg-info me-2">Image</span>
                                                            <span th:if="${message.attachmentType == 'PDF'}" class="badge bg-danger me-2">PDF</span>
                                                            <span th:if="${message.attachmentType == 'DOCUMENT'}" class="badge bg-secondary me-2">Document</span>
                                                            <a th:href="@{/netcafe/message-attachment/{id}(id=${message.id})}" target="_blank" class="text-decoration-none">
                                                                <span th:text="${message.attachmentName}">attachment.pdf</span>
                                                                <small class="text-muted ms-2" th:text="${#numbers.formatDecimal(message.attachmentSize / 1024, 0, 2)} + ' KB'">123 KB</small>
                                                            </a>
                                                        </div>
                                                        <!-- Preview for images -->
                                                        <div th:if="${message.attachmentType == 'IMAGE'}" class="mt-2">
                                                            <a th:href="@{/netcafe/message-attachment/{id}(id=${message.id})}" target="_blank">
                                                                <img th:src="@{/netcafe/message-attachment/{id}(id=${message.id})}" class="img-thumbnail" style="max-height: 150px;" alt="Image attachment">
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div th:if="${messages == null || messages.empty}" class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> No messages yet. Start a conversation with the applicant.
                                    </div>
                                </div>

                                <!-- Message Form -->
                                <form th:action="@{/netcafe/application/{id}/send-message(id=${netCafeApplication.id})}" method="post" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="content" class="form-label">New Message</label>
                                        <textarea class="form-control" id="content" name="content" rows="3" required></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="attachment" class="form-label">Attachment (Optional)</label>
                                        <input type="file" class="form-control" id="attachment" name="attachment">
                                        <div class="form-text">You can attach images, PDFs, or other documents (max 5MB)</div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-send"></i> Send Message
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <a th:href="@{/netcafe/applications}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Applications
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
