// Form validation for NetCafe registration
document.addEventListener('DOMContentLoaded', function() {
    const registrationForm = document.getElementById('registrationForm');

    if (registrationForm) {
        registrationForm.addEventListener('submit', function(event) {
            let isValid = true;

            // Validate name
            const name = document.getElementById('name');
            if (name.value.trim() === '') {
                showError(name, 'Name is required');
                isValid = false;
            } else {
                removeError(name);
            }

            // Validate email
            const email = document.getElementById('email');
            if (email.value.trim() === '') {
                showError(email, 'Email is required');
                isValid = false;
            } else if (!isValidEmail(email.value)) {
                showError(email, 'Please enter a valid email');
                isValid = false;
            } else {
                removeError(email);
            }

            // Validate password
            const password = document.getElementById('password');
            if (password.value.trim() === '') {
                showError(password, 'Password is required');
                isValid = false;
            } else if (password.value.length < 6) {
                showError(password, 'Password must be at least 6 characters');
                isValid = false;
            } else {
                removeError(password);
            }

            // Validate mobile number
            const mobileNumber = document.getElementById('mobileNumber');
            if (mobileNumber.value.trim() === '') {
                showError(mobileNumber, 'Mobile number is required');
                isValid = false;
            } else if (!isValidMobile(mobileNumber.value)) {
                showError(mobileNumber, 'Please enter a valid 10-digit mobile number');
                isValid = false;
            } else {
                removeError(mobileNumber);
            }

            // Validate Aadhar number
            const aadharNumber = document.getElementById('aadharNumber');
            if (aadharNumber.value.trim() === '') {
                showError(aadharNumber, 'Aadhar number is required');
                isValid = false;
            } else if (!isValidAadhar(aadharNumber.value)) {
                showError(aadharNumber, 'Please enter a valid 12-digit Aadhar number');
                isValid = false;
            } else {
                removeError(aadharNumber);
            }

            // Validate PAN number
            const panNumber = document.getElementById('panNumber');
            if (panNumber.value.trim() === '') {
                showError(panNumber, 'PAN number is required');
                isValid = false;
            } else if (!isValidPAN(panNumber.value)) {
                showError(panNumber, 'Please enter a valid PAN number (e.g., **********)');
                isValid = false;
            } else {
                removeError(panNumber);
            }

            // Validate CSC certificate file
            const cscCertificateFile = document.getElementById('cscCertificateFile');
            if (cscCertificateFile.files.length === 0) {
                showError(cscCertificateFile, 'CSC certificate is required');
                isValid = false;
            } else {
                removeError(cscCertificateFile);
            }

            // Validate photo file
            const photoFile = document.getElementById('photoFile');
            if (photoFile.files.length === 0) {
                showError(photoFile, 'Photo is required');
                isValid = false;
            } else {
                removeError(photoFile);
            }

            // Validate Aadhar card photo file
            const aadharCardPhotoFile = document.getElementById('aadharCardPhotoFile');
            if (aadharCardPhotoFile.files.length === 0) {
                showError(aadharCardPhotoFile, 'Aadhar card photo is required');
                isValid = false;
            } else {
                removeError(aadharCardPhotoFile);
            }

            // Validate PAN card photo file
            const panCardPhotoFile = document.getElementById('panCardPhotoFile');
            if (panCardPhotoFile.files.length === 0) {
                showError(panCardPhotoFile, 'PAN card photo is required');
                isValid = false;
            } else {
                removeError(panCardPhotoFile);
            }

            if (!isValid) {
                event.preventDefault();
            }
        });
    }

    // Login form validation
    const loginForm = document.getElementById('loginForm');

    if (loginForm) {
        loginForm.addEventListener('submit', function(event) {
            let isValid = true;

            // Validate email/username
            const email = document.getElementById('email') || document.getElementById('username');
            if (email.value.trim() === '') {
                showError(email, email.id === 'email' ? 'Email is required' : 'Username is required');
                isValid = false;
            } else {
                removeError(email);
            }

            // Validate password
            const password = document.getElementById('password');
            if (password.value.trim() === '') {
                showError(password, 'Password is required');
                isValid = false;
            } else {
                removeError(password);
            }

            if (!isValid) {
                event.preventDefault();
            }
        });
    }
});

// Helper functions
function showError(input, message) {
    const formControl = input.parentElement;
    const errorElement = formControl.querySelector('.invalid-feedback') || document.createElement('div');

    errorElement.className = 'invalid-feedback';
    errorElement.innerText = message;

    if (!formControl.querySelector('.invalid-feedback')) {
        formControl.appendChild(errorElement);
    }

    input.classList.add('is-invalid');
}

function removeError(input) {
    input.classList.remove('is-invalid');
    const errorElement = input.parentElement.querySelector('.invalid-feedback');
    if (errorElement) {
        errorElement.remove();
    }
}

function isValidEmail(email) {
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return re.test(email);
}

function isValidMobile(mobile) {
    const re = /^[0-9]{10}$/;
    return re.test(mobile);
}

function isValidAadhar(aadhar) {
    const re = /^[0-9]{12}$/;
    return re.test(aadhar);
}

function isValidPAN(pan) {
    const re = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return re.test(pan);
}
