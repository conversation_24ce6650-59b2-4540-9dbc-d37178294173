package com.example.demo.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "netcafe_applications")
public class NetCafeApplication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "netcafe_user_id", nullable = false)
    private NetCafeUser netCafeUser;

    @ManyToOne
    @JoinColumn(name = "application_id", nullable = false)
    private SchemeApplication application;

    @Column(nullable = false)
    private LocalDateTime assignmentDate;

    @Column
    private String status; // PENDING, PROCESSING, COMPLETED, CONFIRMED, BOND_BROKEN

    @Column
    private String remarks;

    @Column
    private LocalDateTime completionDate;

    @Column
    private LocalDateTime confirmationDate;

    @Column
    private Boolean bondBroken = false;

    @Column
    private LocalDateTime bondBrokenDate;

    @Column
    private String bondBreakReason;

    @Column
    private String bondBreakInitiator; // USER or NETCAFE

    @Column
    private Boolean readyForCompletion = false; // NetCafe marks this when ready for user confirmation

    @Column
    private LocalDateTime readyForCompletionDate;

    @Column
    private Boolean userConfirmedCompletion = false; // User confirms completion

    @Column
    private LocalDateTime userConfirmationDate;

    // Default constructor
    public NetCafeApplication() {
        this.assignmentDate = LocalDateTime.now();
        this.status = "PENDING";
    }

    // Constructor with fields
    public NetCafeApplication(NetCafeUser netCafeUser, SchemeApplication application) {
        this.netCafeUser = netCafeUser;
        this.application = application;
        this.assignmentDate = LocalDateTime.now();
        this.status = "PENDING";
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public NetCafeUser getNetCafeUser() {
        return netCafeUser;
    }

    public void setNetCafeUser(NetCafeUser netCafeUser) {
        this.netCafeUser = netCafeUser;
    }

    public SchemeApplication getApplication() {
        return application;
    }

    public void setApplication(SchemeApplication application) {
        this.application = application;
    }

    public LocalDateTime getAssignmentDate() {
        return assignmentDate;
    }

    public void setAssignmentDate(LocalDateTime assignmentDate) {
        this.assignmentDate = assignmentDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public LocalDateTime getConfirmationDate() {
        return confirmationDate;
    }

    public void setConfirmationDate(LocalDateTime confirmationDate) {
        this.confirmationDate = confirmationDate;
    }

    public Boolean getBondBroken() {
        return bondBroken;
    }

    public void setBondBroken(Boolean bondBroken) {
        this.bondBroken = bondBroken;
    }

    public LocalDateTime getBondBrokenDate() {
        return bondBrokenDate;
    }

    public void setBondBrokenDate(LocalDateTime bondBrokenDate) {
        this.bondBrokenDate = bondBrokenDate;
    }

    public String getBondBreakReason() {
        return bondBreakReason;
    }

    public void setBondBreakReason(String bondBreakReason) {
        this.bondBreakReason = bondBreakReason;
    }

    public String getBondBreakInitiator() {
        return bondBreakInitiator;
    }

    public void setBondBreakInitiator(String bondBreakInitiator) {
        this.bondBreakInitiator = bondBreakInitiator;
    }

    public Boolean getReadyForCompletion() {
        return readyForCompletion;
    }

    public void setReadyForCompletion(Boolean readyForCompletion) {
        this.readyForCompletion = readyForCompletion;
    }

    public LocalDateTime getReadyForCompletionDate() {
        return readyForCompletionDate;
    }

    public void setReadyForCompletionDate(LocalDateTime readyForCompletionDate) {
        this.readyForCompletionDate = readyForCompletionDate;
    }

    public Boolean getUserConfirmedCompletion() {
        return userConfirmedCompletion;
    }

    public void setUserConfirmedCompletion(Boolean userConfirmedCompletion) {
        this.userConfirmedCompletion = userConfirmedCompletion;
    }

    public LocalDateTime getUserConfirmationDate() {
        return userConfirmationDate;
    }

    public void setUserConfirmationDate(LocalDateTime userConfirmationDate) {
        this.userConfirmationDate = userConfirmationDate;
    }
}
