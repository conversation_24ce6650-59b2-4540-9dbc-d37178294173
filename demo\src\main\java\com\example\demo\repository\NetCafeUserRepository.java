package com.example.demo.repository;

import com.example.demo.model.NetCafeUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface NetCafeUserRepository extends JpaRepository<NetCafeUser, Long> {

    Optional<NetCafeUser> findByEmail(String email);

    boolean existsByEmail(String email);

    boolean existsByAadharNumber(String aadharNumber);

    boolean existsByPanNumber(String panNumber);

    List<NetCafeUser> findByApproved(boolean approved);

    List<NetCafeUser> findByActiveTrue();
}
