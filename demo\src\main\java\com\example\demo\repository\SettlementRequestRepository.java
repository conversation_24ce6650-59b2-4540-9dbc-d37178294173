package com.example.demo.repository;

import com.example.demo.model.NetCafeUser;
import com.example.demo.model.SettlementRequest;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SettlementRequestRepository extends JpaRepository<SettlementRequest, Long> {

    // Find settlement requests by NetCafe user
    List<SettlementRequest> findByNetCafeUserOrderByRequestDateDesc(NetCafeUser netCafeUser);

    // Find settlement requests by status
    List<SettlementRequest> findByStatusOrderByRequestDateDesc(String status);

    // Find pending settlement requests
    List<SettlementRequest> findByStatusOrderByRequestDateAsc(String status);

    // Find all settlement requests ordered by request date
    List<SettlementRequest> findAllByOrderByRequestDateDesc();

    // Find settlement requests by NetCafe user and status
    List<SettlementRequest> findByNetCafeUserAndStatusOrderByRequestDateDesc(NetCafeUser netCafeUser, String status);

    // Check if NetCafe user has any pending settlement requests
    @Query("SELECT COUNT(sr) FROM SettlementRequest sr WHERE sr.netCafeUser = ?1 AND sr.status = 'PENDING'")
    long countPendingRequestsByNetCafeUser(NetCafeUser netCafeUser);

    // Get total requested amount by NetCafe user and status
    @Query("SELECT COALESCE(SUM(sr.requestedAmount), 0) FROM SettlementRequest sr WHERE sr.netCafeUser = ?1 AND sr.status = ?2")
    java.math.BigDecimal getTotalRequestedAmountByNetCafeUserAndStatus(NetCafeUser netCafeUser, String status);
}
