package com.example.demo.controller;

import com.example.demo.model.Admin;
import com.example.demo.model.ApplicationDocument;
import com.example.demo.model.Document;
import com.example.demo.model.NetCafeApplication;
import com.example.demo.model.NetCafeUser;
import com.example.demo.model.PaymentRecord;
import com.example.demo.model.Scheme;
import com.example.demo.model.SchemeApplication;

import java.time.LocalDateTime;
import com.example.demo.service.DocumentService;
import com.example.demo.service.NetCafeApplicationService;
import com.example.demo.service.NetCafeUserService;
import com.example.demo.service.PaymentService;
import com.example.demo.service.SchemeApplicationService;
import com.example.demo.service.SchemeService;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.example.demo.util.QRCodeGenerator;
import com.google.zxing.WriterException;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/admin/schemes")
public class SchemeController {

    @Autowired
    private SchemeService schemeService;

    @Autowired
    private SchemeApplicationService schemeApplicationService;

    @Autowired
    private NetCafeApplicationService netCafeApplicationService;

    @Autowired
    private NetCafeUserService netCafeUserService;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private PaymentService paymentService;

    @GetMapping
    public String listSchemes(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        List<Scheme> schemes = schemeService.getAllSchemes();
        model.addAttribute("admin", admin);
        model.addAttribute("schemes", schemes);

        return "admin/schemes/list";
    }

    @GetMapping("/create")
    public String showCreateForm(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        // Get all available documents for selection
        List<Document> documents = documentService.getAllActiveDocuments();

        model.addAttribute("admin", admin);
        model.addAttribute("scheme", new Scheme());
        model.addAttribute("documents", documents);
        model.addAttribute("categories", getDocumentCategories(documents));

        return "admin/schemes/create";
    }

    // Helper method to get unique document categories
    private List<String> getDocumentCategories(List<Document> documents) {
        return documents.stream()
                .map(Document::getCategory)
                .distinct()
                .sorted()
                .toList();
    }

    @PostMapping("/create")
    public String createScheme(
            @ModelAttribute Scheme scheme,
            @RequestParam(value = "documentFile", required = false) MultipartFile documentFile,
            @RequestParam(value = "documentIds", required = false) List<Long> documentIds,
            @RequestParam(value = "paymentAmount", required = false) Double paymentAmount,
            @RequestParam(value = "upiId", required = false) String upiId,
            @RequestParam(value = "paymentDetails", required = false) String paymentDetails,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            // Generate QR code if payment amount is provided
            if (paymentAmount != null && paymentAmount > 0 && upiId != null && !upiId.isEmpty()) {
                try {
                    byte[] qrCode = QRCodeGenerator.generateUpiQRCode(
                            upiId,
                            "Scheme Payment",
                            paymentAmount,
                            "Payment for " + scheme.getName());
                    scheme.setPaymentQrCode(qrCode);
                    scheme.setPaymentAmount(paymentAmount);
                    scheme.setPaymentDetails(paymentDetails);
                } catch (WriterException e) {
                    redirectAttributes.addFlashAttribute("error", "Error generating QR code: " + e.getMessage());
                    return "redirect:/admin/schemes/create";
                }
            }

            schemeService.createScheme(scheme, documentFile, documentIds);
            redirectAttributes.addFlashAttribute("success", "Scheme created successfully");
            return "redirect:/admin/schemes";
        } catch (IOException e) {
            redirectAttributes.addFlashAttribute("error", "Error uploading document: " + e.getMessage());
            return "redirect:/admin/schemes/create";
        }
    }

    @GetMapping("/edit/{id}")
    public String showEditForm(
            @PathVariable Long id,
            HttpSession session,
            Model model,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        Optional<Scheme> schemeOpt = schemeService.getSchemeById(id);

        if (schemeOpt.isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "Scheme not found");
            return "redirect:/admin/schemes";
        }

        // Get all available documents for selection
        List<Document> allDocuments = documentService.getAllActiveDocuments();

        // Get currently selected documents for this scheme
        List<Document> selectedDocuments = schemeService.getRequiredDocumentsForScheme(id);

        // Get document IDs for pre-selecting in the form
        List<Long> selectedDocumentIds = selectedDocuments.stream()
                .map(Document::getId)
                .toList();

        model.addAttribute("admin", admin);
        model.addAttribute("scheme", schemeOpt.get());
        model.addAttribute("documents", allDocuments);
        model.addAttribute("selectedDocumentIds", selectedDocumentIds);
        model.addAttribute("categories", getDocumentCategories(allDocuments));

        return "admin/schemes/edit";
    }

    @PostMapping("/edit/{id}")
    public String updateScheme(
            @PathVariable Long id,
            @ModelAttribute Scheme scheme,
            @RequestParam(value = "documentFile", required = false) MultipartFile documentFile,
            @RequestParam(value = "documentIds", required = false) List<Long> documentIds,
            @RequestParam(value = "paymentAmount", required = false) Double paymentAmount,
            @RequestParam(value = "upiId", required = false) String upiId,
            @RequestParam(value = "paymentDetails", required = false) String paymentDetails,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            // Generate QR code if payment amount is provided
            if (paymentAmount != null && paymentAmount > 0 && upiId != null && !upiId.isEmpty()) {
                try {
                    byte[] qrCode = QRCodeGenerator.generateUpiQRCode(
                            upiId,
                            "Scheme Payment",
                            paymentAmount,
                            "Payment for " + scheme.getName());
                    scheme.setPaymentQrCode(qrCode);
                    scheme.setPaymentAmount(paymentAmount);
                    scheme.setPaymentDetails(paymentDetails);
                } catch (WriterException e) {
                    redirectAttributes.addFlashAttribute("error", "Error generating QR code: " + e.getMessage());
                    return "redirect:/admin/schemes/edit/" + id;
                }
            }

            schemeService.updateScheme(id, scheme, documentFile, documentIds);
            redirectAttributes.addFlashAttribute("success", "Scheme updated successfully");
            return "redirect:/admin/schemes";
        } catch (IOException e) {
            redirectAttributes.addFlashAttribute("error", "Error uploading document: " + e.getMessage());
            return "redirect:/admin/schemes/edit/" + id;
        } catch (RuntimeException e) {
            redirectAttributes.addFlashAttribute("error", e.getMessage());
            return "redirect:/admin/schemes";
        }
    }

    @PostMapping("/delete/{id}")
    public String deleteScheme(
            @PathVariable Long id,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            schemeService.deleteScheme(id);
            redirectAttributes.addFlashAttribute("success", "Scheme deleted successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error deleting scheme: " + e.getMessage());
        }

        return "redirect:/admin/schemes";
    }

    @PostMapping("/activate/{id}")
    public String activateScheme(
            @PathVariable Long id,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            schemeService.activateScheme(id);
            redirectAttributes.addFlashAttribute("success", "Scheme activated successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error activating scheme: " + e.getMessage());
        }

        return "redirect:/admin/schemes";
    }

    @PostMapping("/deactivate/{id}")
    public String deactivateScheme(
            @PathVariable Long id,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            schemeService.deactivateScheme(id);
            redirectAttributes.addFlashAttribute("success", "Scheme deactivated successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error deactivating scheme: " + e.getMessage());
        }

        return "redirect:/admin/schemes";
    }

    @GetMapping("/applications")
    public String listApplications(
            HttpSession session,
            Model model,
            @RequestParam(required = false) String status) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        List<SchemeApplication> applications;

        if (status != null && !status.isEmpty()) {
            applications = schemeApplicationService.getApplicationsByStatus(status);
        } else {
            applications = schemeApplicationService.getAllApplications();
        }

        model.addAttribute("admin", admin);
        model.addAttribute("applications", applications);
        model.addAttribute("currentStatus", status);

        return "admin/schemes/applications";
    }

    @GetMapping("/applications/{id}")
    public String viewApplication(
            @PathVariable Long id,
            HttpSession session,
            Model model,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

        if (applicationOpt.isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "Application not found");
            return "redirect:/admin/schemes/applications";
        }

        SchemeApplication application = applicationOpt.get();

        // Debug logging
        System.out.println("Application ID: " + application.getId());
        System.out.println("User: " + (application.getUser() != null ? application.getUser().getName() : "null"));
        System.out.println("Scheme: " + (application.getScheme() != null ? application.getScheme().getName() : "null"));
        System.out.println("Status: " + application.getStatus());
        System.out.println("Documents count: " + (application.getApplicationDocuments() != null ? application.getApplicationDocuments().size() : 0));

        if (application.getApplicationDocuments() != null) {
            for (ApplicationDocument doc : application.getApplicationDocuments()) {
                System.out.println("Document: " + (doc.getDocument() != null ? doc.getDocument().getName() : "null") +
                                  ", File: " + (doc.getDocumentFile() != null ? "available" : "null"));
            }
        }

        // Get required documents for this scheme if scheme exists
        List<Document> requiredDocuments = new ArrayList<>();
        if (application.getScheme() != null) {
            requiredDocuments = schemeService.getRequiredDocumentsForScheme(application.getScheme().getId());
            System.out.println("Required documents count: " + requiredDocuments.size());
        }

        // Check if application is assigned to a NetCafe
        boolean isAssignedToNetCafe = netCafeApplicationService.isApplicationAssigned(application.getId());
        System.out.println("Is assigned to NetCafe: " + isAssignedToNetCafe);

        List<NetCafeUser> availableNetCafeUsers = netCafeUserService.getAllActiveNetCafeUsers();
        System.out.println("Available NetCafe users: " + availableNetCafeUsers.size());

        model.addAttribute("admin", admin);
        model.addAttribute("application", application);
        model.addAttribute("requiredDocuments", requiredDocuments);
        model.addAttribute("isAssignedToNetCafe", isAssignedToNetCafe);
        model.addAttribute("availableNetCafeUsers", availableNetCafeUsers);

        return "admin/schemes/application-details-safe";
    }

    @PostMapping("/applications/{id}/assign-netcafe")
    public String assignApplicationToNetCafe(
            @PathVariable Long id,
            @RequestParam Long netCafeUserId,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);
            Optional<NetCafeUser> netCafeUserOpt = netCafeUserService.getNetCafeUserById(netCafeUserId);

            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/admin/schemes/applications";
            }

            if (netCafeUserOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "NetCafe user not found");
                return "redirect:/admin/schemes/applications/" + id;
            }

            SchemeApplication application = applicationOpt.get();
            NetCafeUser netCafeUser = netCafeUserOpt.get();

            // Assign application to NetCafe
            netCafeApplicationService.assignApplication(netCafeUser, application);

            redirectAttributes.addFlashAttribute("success", "Application assigned to NetCafe successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error assigning application to NetCafe: " + e.getMessage());
        }

        return "redirect:/admin/schemes/applications/" + id;
    }

    @PostMapping("/applications/{id}/update-status")
    public String updateApplicationStatus(
            @PathVariable Long id,
            @RequestParam String status,
            @RequestParam(required = false) String remarks,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            schemeApplicationService.updateApplicationStatus(id, status, remarks);
            redirectAttributes.addFlashAttribute("success", "Application status updated successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error updating application status: " + e.getMessage());
        }

        return "redirect:/admin/schemes/applications";
    }

    @PostMapping("/applications/{id}/verify-payment")
    public String verifyPayment(
            @PathVariable Long id,
            @RequestParam(required = false) String remarks,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            // Update the application payment status
            SchemeApplication application = schemeApplicationService.updatePaymentStatus(id, "VERIFIED", remarks);

            // Find and update any associated payment records
            List<PaymentRecord> payments = paymentService.getPaymentsForApplication(application);

            System.out.println("DEBUG - Verify Payment - Found " + payments.size() + " payment records for application ID: " + id);

            for (PaymentRecord payment : payments) {
                if ("USER_PAYMENT".equals(payment.getPaymentType()) && "PENDING".equals(payment.getStatus())) {
                    System.out.println("DEBUG - Verify Payment - Verifying payment ID: " + payment.getId());
                    paymentService.verifyUserPayment(payment.getId(), remarks);
                }
            }

            redirectAttributes.addFlashAttribute("success", "Payment verified successfully");
        } catch (Exception e) {
            System.out.println("DEBUG - Verify Payment - Error: " + e.getMessage());
            e.printStackTrace();
            redirectAttributes.addFlashAttribute("error", "Error verifying payment: " + e.getMessage());
        }

        return "redirect:/admin/schemes/applications/" + id;
    }

    @GetMapping("/document/{id}")
    public ResponseEntity<byte[]> getSchemeDocument(@PathVariable Long id) {
        Optional<Scheme> schemeOpt = schemeService.getSchemeById(id);

        if (schemeOpt.isPresent()) {
            Scheme scheme = schemeOpt.get();
            byte[] documentData = scheme.getSchemeDocument();

            if (documentData != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType(scheme.getDocumentContentType()));
                return new ResponseEntity<>(documentData, headers, HttpStatus.OK);
            }
        }

        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @GetMapping("/applications/document/{id}")
    public ResponseEntity<byte[]> getApplicationDocument(@PathVariable Long id) {
        Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

        if (applicationOpt.isPresent()) {
            SchemeApplication application = applicationOpt.get();
            byte[] documentData = application.getSupportingDocument();

            if (documentData != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType(application.getDocumentContentType()));
                return new ResponseEntity<>(documentData, headers, HttpStatus.OK);
            }
        }

        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @GetMapping("/applications/document/{appId}/{docId}")
    public ResponseEntity<byte[]> getSpecificApplicationDocument(
            @PathVariable Long appId,
            @PathVariable Long docId) {

        ApplicationDocument appDoc = schemeApplicationService.getApplicationDocument(appId, docId);

        if (appDoc != null && appDoc.getDocumentFile() != null) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(appDoc.getDocumentContentType()));
            headers.setContentDispositionFormData("attachment", appDoc.getDocumentName());
            return new ResponseEntity<>(appDoc.getDocumentFile(), headers, HttpStatus.OK);
        }

        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @GetMapping("/payment-qr/{id}")
    public ResponseEntity<byte[]> getPaymentQRCode(@PathVariable Long id) {
        Optional<Scheme> schemeOpt = schemeService.getSchemeById(id);

        if (schemeOpt.isPresent()) {
            Scheme scheme = schemeOpt.get();
            byte[] qrCodeData = scheme.getPaymentQrCode();

            if (qrCodeData != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.IMAGE_PNG);
                return new ResponseEntity<>(qrCodeData, headers, HttpStatus.OK);
            }
        }

        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @GetMapping("/applications/payment-screenshot/{id}")
    public ResponseEntity<byte[]> getPaymentScreenshot(@PathVariable Long id) {
        Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

        if (applicationOpt.isPresent()) {
            SchemeApplication application = applicationOpt.get();
            byte[] screenshotData = application.getPaymentScreenshot();

            if (screenshotData != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType(application.getPaymentScreenshotContentType()));
                return new ResponseEntity<>(screenshotData, headers, HttpStatus.OK);
            }
        }

        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @GetMapping("/applications/{id}/break-bond-form")
    public String showBreakBondForm(
            @PathVariable Long id,
            HttpSession session,
            Model model,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            // Find the application
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/admin/schemes/applications";
            }

            SchemeApplication application = applicationOpt.get();

            // Find the NetCafe application
            List<NetCafeApplication> applications = netCafeApplicationService.getApplicationsBySchemeApplicationId(id);

            if (applications.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "No NetCafe application found for this scheme application");
                return "redirect:/admin/schemes/applications/" + id;
            }

            // Get the latest NetCafe application
            NetCafeApplication netCafeApplication = applications.get(0);

            model.addAttribute("admin", admin);
            model.addAttribute("application", application);
            model.addAttribute("netCafeApplication", netCafeApplication);

            return "admin/schemes/break-bond-form";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error loading break bond form: " + e.getMessage());
            return "redirect:/admin/schemes/applications/" + id;
        }
    }

    @PostMapping("/applications/{id}/break-bond")
    public String breakBond(
            @PathVariable Long id,
            @RequestParam(required = false) String reason,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            // Find the application
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/admin/schemes/applications";
            }

            SchemeApplication application = applicationOpt.get();

            // Find the NetCafe application
            List<NetCafeApplication> applications = netCafeApplicationService.getApplicationsBySchemeApplicationId(id);

            if (applications.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "No NetCafe application found for this scheme application");
                return "redirect:/admin/schemes/applications/" + id;
            }

            // Get the latest NetCafe application
            NetCafeApplication netCafeApplication = applications.get(0);

            // Break the bond
            netCafeApplication.setBondBroken(true);
            netCafeApplication.setBondBreakReason(reason);
            netCafeApplication.setBondBrokenDate(LocalDateTime.now());
            netCafeApplication.setBondBreakInitiator("ADMIN");
            netCafeApplicationService.saveApplication(netCafeApplication);

            // Update the scheme application
            application.setBondBroken(true);
            application.setBondBreakReason(reason);
            application.setBondBrokenDate(LocalDateTime.now());
            application.setStatus("BOND_BROKEN");
            schemeApplicationService.saveApplication(application);

            redirectAttributes.addFlashAttribute("success", "Bond broken successfully");
            return "redirect:/admin/schemes/applications/" + id;
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error breaking bond: " + e.getMessage());
            return "redirect:/admin/schemes/applications/" + id;
        }
    }
}
