<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settlement Request - NetCafe Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/netcafe/dashboard}">
                                <i class="bi bi-house"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/netcafe/applications}">
                                <i class="bi bi-file-text"></i> Applications
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/netcafe/payments}">
                                <i class="bi bi-credit-card"></i> Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" th:href="@{/netcafe/settlement}">
                                <i class="bi bi-bank"></i> Settlement
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/netcafe/logout}">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Settlement Request</h1>
                </div>

                <!-- Flash Messages -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Available Balance Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-wallet2"></i> Available Balance
                    </div>
                    <div class="card-body">
                        <h3 class="text-success">₹<span th:text="${availableBalance}">0.00</span></h3>
                        <p class="text-muted">This is your available balance for settlement after deducting pending requests.</p>
                    </div>
                </div>

                <!-- Settlement Request Form -->
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-send"></i> Request Settlement
                    </div>
                    <div class="card-body">
                        <form th:action="@{/netcafe/settlement/request}" method="post" id="settlementForm">
                            <!-- Amount -->
                            <div class="mb-3">
                                <label for="requestedAmount" class="form-label">Settlement Amount *</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="requestedAmount" name="requestedAmount" 
                                           step="0.01" min="1" th:max="${availableBalance}" required>
                                </div>
                                <div class="form-text">Maximum available: ₹<span th:text="${availableBalance}">0.00</span></div>
                            </div>

                            <!-- Payment Method -->
                            <div class="mb-3">
                                <label class="form-label">Payment Method *</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="paymentMethod" id="bankAccount" value="BANK_ACCOUNT" checked>
                                    <label class="form-check-label" for="bankAccount">
                                        <i class="bi bi-bank"></i> Bank Account
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="paymentMethod" id="upi" value="UPI">
                                    <label class="form-check-label" for="upi">
                                        <i class="bi bi-phone"></i> UPI
                                    </label>
                                </div>
                            </div>

                            <!-- Bank Account Details -->
                            <div id="bankAccountDetails" class="payment-details">
                                <h5>Bank Account Details</h5>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="accountHolderName" class="form-label">Account Holder Name *</label>
                                            <input type="text" class="form-control" id="accountHolderName" name="accountHolderName">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="bankAccountNumber" class="form-label">Account Number *</label>
                                            <input type="text" class="form-control" id="bankAccountNumber" name="bankAccountNumber">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="ifscCode" class="form-label">IFSC Code *</label>
                                            <input type="text" class="form-control" id="ifscCode" name="ifscCode" style="text-transform: uppercase;">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="bankName" class="form-label">Bank Name *</label>
                                            <input type="text" class="form-control" id="bankName" name="bankName">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- UPI Details -->
                            <div id="upiDetails" class="payment-details" style="display: none;">
                                <h5>UPI Details</h5>
                                <div class="mb-3">
                                    <label for="upiId" class="form-label">UPI ID *</label>
                                    <input type="text" class="form-control" id="upiId" name="upiId" placeholder="example@paytm">
                                    <div class="form-text">Enter your UPI ID (e.g., yourname@paytm, yourname@phonepe)</div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-send"></i> Submit Settlement Request
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Information Card -->
                <div class="card mt-4">
                    <div class="card-header">
                        <i class="bi bi-info-circle"></i> Settlement Information
                    </div>
                    <div class="card-body">
                        <h6>Processing Time</h6>
                        <p>Settlement requests are typically processed within 2-3 business days after admin approval.</p>
                        
                        <h6>Requirements</h6>
                        <ul>
                            <li>Minimum settlement amount: ₹100</li>
                            <li>You can only have one pending settlement request at a time</li>
                            <li>Bank account details must be accurate for successful transfer</li>
                            <li>UPI ID must be active and verified</li>
                        </ul>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle payment method details
        document.querySelectorAll('input[name="paymentMethod"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const bankDetails = document.getElementById('bankAccountDetails');
                const upiDetails = document.getElementById('upiDetails');
                
                if (this.value === 'BANK_ACCOUNT') {
                    bankDetails.style.display = 'block';
                    upiDetails.style.display = 'none';
                    
                    // Make bank fields required
                    document.getElementById('accountHolderName').required = true;
                    document.getElementById('bankAccountNumber').required = true;
                    document.getElementById('ifscCode').required = true;
                    document.getElementById('bankName').required = true;
                    document.getElementById('upiId').required = false;
                } else {
                    bankDetails.style.display = 'none';
                    upiDetails.style.display = 'block';
                    
                    // Make UPI field required
                    document.getElementById('upiId').required = true;
                    document.getElementById('accountHolderName').required = false;
                    document.getElementById('bankAccountNumber').required = false;
                    document.getElementById('ifscCode').required = false;
                    document.getElementById('bankName').required = false;
                }
            });
        });

        // IFSC code uppercase
        document.getElementById('ifscCode').addEventListener('input', function() {
            this.value = this.value.toUpperCase();
        });
    </script>
</body>
</html>
