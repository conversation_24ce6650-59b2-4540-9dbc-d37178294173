<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header">
                <h2>User Profile Test</h2>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <p><strong>ID:</strong></p>
                    </div>
                    <div class="col-md-8">
                        <p th:text="${user.id}">1</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <p><strong>Name:</strong></p>
                    </div>
                    <div class="col-md-8">
                        <p th:text="${user.name}"><PERSON>e</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <p><strong>Email:</strong></p>
                    </div>
                    <div class="col-md-8">
                        <p th:text="${user.email}"><EMAIL></p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <p><strong>Mobile Number:</strong></p>
                    </div>
                    <div class="col-md-8">
                        <p th:text="${user.mobileNumber}">1234567890</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <p><strong>Address:</strong></p>
                    </div>
                    <div class="col-md-8">
                        <p th:text="${user.address}">123 Main St</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <p><strong>Registration Date:</strong></p>
                    </div>
                    <div class="col-md-8">
                        <p th:text="${#temporals.format(user.registrationDate, 'dd-MM-yyyy')}">01-01-2023</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <p><strong>Active:</strong></p>
                    </div>
                    <div class="col-md-8">
                        <p th:text="${user.active}">true</p>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="/user/dashboard" class="btn btn-primary">Back to Dashboard</a>
            </div>
        </div>
    </div>
</body>
</html>
