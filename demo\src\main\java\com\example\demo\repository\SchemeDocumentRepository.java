package com.example.demo.repository;

import com.example.demo.model.Document;
import com.example.demo.model.Scheme;
import com.example.demo.model.SchemeDocument;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SchemeDocumentRepository extends JpaRepository<SchemeDocument, Long> {
    
    List<SchemeDocument> findByScheme(Scheme scheme);
    
    List<SchemeDocument> findByDocument(Document document);
    
    List<SchemeDocument> findBySchemeAndRequired(Scheme scheme, boolean required);
    
    void deleteBySchemeAndDocument(Scheme scheme, Document document);
}
