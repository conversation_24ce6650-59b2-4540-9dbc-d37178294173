package com.example.demo.controller;

import com.example.demo.model.Admin;
import com.example.demo.model.NetCafeCommission;
import com.example.demo.model.Scheme;
import com.example.demo.repository.NetCafeCommissionRepository;
import com.example.demo.service.SchemeService;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/admin/commissions")
public class NetCafeCommissionController {

    @Autowired
    private NetCafeCommissionRepository netCafeCommissionRepository;

    @Autowired
    private SchemeService schemeService;

    @GetMapping
    public String listCommissions(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        List<NetCafeCommission> commissions = netCafeCommissionRepository.findAll();
        model.addAttribute("admin", admin);
        model.addAttribute("commissions", commissions);

        return "admin/commissions/list";
    }

    @GetMapping("/create")
    public String showCreateForm(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        List<Scheme> schemes = schemeService.getAllSchemes();
        model.addAttribute("admin", admin);
        model.addAttribute("commission", new NetCafeCommission());
        model.addAttribute("schemes", schemes);

        return "admin/commissions/create";
    }

    @PostMapping("/create")
    public String createCommission(
            @RequestParam Long schemeId,
            @RequestParam String commissionType,
            @RequestParam(required = false) BigDecimal commissionAmount,
            @RequestParam(required = false) BigDecimal commissionPercentage,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            Optional<Scheme> schemeOpt = schemeService.getSchemeById(schemeId);
            if (schemeOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Scheme not found");
                return "redirect:/admin/commissions/create";
            }

            Scheme scheme = schemeOpt.get();

            // Deactivate any existing commission for this scheme
            Optional<NetCafeCommission> existingCommissionOpt = netCafeCommissionRepository.findBySchemeAndIsActiveTrue(scheme);
            if (existingCommissionOpt.isPresent()) {
                NetCafeCommission existing = existingCommissionOpt.get();
                existing.setIsActive(false);
                existing.setUpdatedAt(LocalDateTime.now());
                netCafeCommissionRepository.save(existing);
            }

            // Create new commission
            NetCafeCommission commission = new NetCafeCommission();
            commission.setScheme(scheme);
            commission.setIsFixedAmount("fixed".equals(commissionType));

            if ("fixed".equals(commissionType)) {
                if (commissionAmount == null || commissionAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    redirectAttributes.addFlashAttribute("error", "Commission amount must be greater than zero");
                    return "redirect:/admin/commissions/create";
                }
                commission.setCommissionAmount(commissionAmount);
                commission.setCommissionPercentage(BigDecimal.ZERO);
            } else {
                if (commissionPercentage == null || commissionPercentage.compareTo(BigDecimal.ZERO) <= 0 || commissionPercentage.compareTo(new BigDecimal("100")) > 0) {
                    redirectAttributes.addFlashAttribute("error", "Commission percentage must be between 0 and 100");
                    return "redirect:/admin/commissions/create";
                }
                commission.setCommissionPercentage(commissionPercentage);
                commission.setCommissionAmount(BigDecimal.ZERO);
            }

            commission.setIsActive(true);
            commission.setCreatedAt(LocalDateTime.now());
            commission.setUpdatedAt(LocalDateTime.now());
            commission.setCreatedBy(admin.getUsername());

            netCafeCommissionRepository.save(commission);

            redirectAttributes.addFlashAttribute("success", "Commission created successfully");
            return "redirect:/admin/commissions";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error creating commission: " + e.getMessage());
            return "redirect:/admin/commissions/create";
        }
    }

    @PostMapping("/deactivate/{id}")
    public String deactivateCommission(
            @PathVariable Long id,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            Optional<NetCafeCommission> commissionOpt = netCafeCommissionRepository.findById(id);
            if (commissionOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Commission not found");
                return "redirect:/admin/commissions";
            }

            NetCafeCommission commission = commissionOpt.get();
            commission.setIsActive(false);
            commission.setUpdatedAt(LocalDateTime.now());
            netCafeCommissionRepository.save(commission);

            redirectAttributes.addFlashAttribute("success", "Commission deactivated successfully");
            return "redirect:/admin/commissions";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error deactivating commission: " + e.getMessage());
            return "redirect:/admin/commissions";
        }
    }
}
