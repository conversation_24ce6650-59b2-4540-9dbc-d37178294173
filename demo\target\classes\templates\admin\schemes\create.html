<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Scheme</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .header-card {
            background-color: #dc3545;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .btn-danger {
            background-color: #dc3545;
            border: none;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes/applications}">
                            <i class="bi bi-file-earmark-text"></i> Scheme Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/schemes}">
                            <i class="bi bi-list-check"></i> Manage Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/documents}">
                            <i class="bi bi-file-earmark"></i> Document Types
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2>Create New Scheme</h2>
                    <p>Add a new government scheme to the system</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-body">
                        <form th:action="@{/admin/schemes/create}" method="post" enctype="multipart/form-data" th:object="${scheme}">
                            <div class="mb-3">
                                <label for="name" class="form-label">Scheme Name</label>
                                <input type="text" class="form-control" id="name" th:field="*{name}" required>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="4" required></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="eligibility" class="form-label">Eligibility Criteria</label>
                                <textarea class="form-control" id="eligibility" th:field="*{eligibility}" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="benefits" class="form-label">Benefits</label>
                                <textarea class="form-control" id="benefits" th:field="*{benefits}" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="applicationProcess" class="form-label">Application Process</label>
                                <textarea class="form-control" id="applicationProcess" th:field="*{applicationProcess}" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="requiredDocuments" class="form-label">Required Documents (Manual Entry)</label>
                                <textarea class="form-control" id="requiredDocuments" th:field="*{requiredDocuments}" rows="3"></textarea>
                                <small class="text-muted">You can manually enter required documents here, or select from the document types below.</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Required Document Types</label>
                                <div class="accordion" id="documentAccordion">
                                    <div th:each="category : ${categories}" class="accordion-item">
                                        <h2 class="accordion-header" th:id="'heading-' + ${category.replace(' ', '-')}">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                                    th:data-bs-target="'#collapse-' + ${category.replace(' ', '-')}" aria-expanded="false"
                                                    th:aria-controls="'collapse-' + ${category.replace(' ', '-')}">
                                                <span th:text="${category}">Category Name</span>
                                            </button>
                                        </h2>
                                        <div th:id="'collapse-' + ${category.replace(' ', '-')}" class="accordion-collapse collapse"
                                             th:aria-labelledby="'heading-' + ${category.replace(' ', '-')}" data-bs-parent="#documentAccordion">
                                            <div class="accordion-body">
                                                <div class="row">
                                                    <div th:each="doc : ${documents}" class="col-md-6 mb-2" th:if="${doc.category == category}">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="documentIds" th:value="${doc.id}"
                                                                   th:id="'doc-' + ${doc.id}" th:title="${doc.name}" title="Select document: [[${doc.name}]]">
                                                            <label class="form-check-label" th:for="'doc-' + ${doc.id}" th:text="${doc.name}">
                                                                Document Name
                                                            </label>
                                                            <small class="text-muted d-block" th:if="${doc.description}" th:text="${doc.description}">
                                                                Document description
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="documentFile" class="form-label">Scheme Document</label>
                                <input type="file" class="form-control" id="documentFile" name="documentFile">
                                <small class="text-muted">Upload a PDF document with detailed information about the scheme.</small>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="active" th:field="*{active}" checked>
                                <label class="form-check-label" for="active">Active</label>
                                <small class="text-muted d-block">If checked, the scheme will be visible to users.</small>
                            </div>

                            <!-- Payment Settings -->
                            <div class="card mb-3">
                                <div class="card-header bg-info text-white">
                                    <i class="bi bi-credit-card me-2"></i> Payment Settings
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="paymentAmount" class="form-label">Payment Amount (₹)</label>
                                        <input type="number" class="form-control" id="paymentAmount" name="paymentAmount"
                                               step="0.01" min="0">
                                        <small class="text-muted">Enter the amount to be paid by applicants (leave empty if no payment is required)</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="upiId" class="form-label">UPI ID</label>
                                        <input type="text" class="form-control" id="upiId" name="upiId" placeholder="example@upi">
                                        <small class="text-muted">Enter your UPI ID to generate a payment QR code</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="paymentDetails" class="form-label">Payment Instructions</label>
                                        <textarea class="form-control" id="paymentDetails" name="paymentDetails" rows="3"></textarea>
                                        <small class="text-muted">Additional payment instructions or details for applicants</small>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a th:href="@{/admin/schemes}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-danger">Create Scheme</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
