package com.example.demo.service;

import com.example.demo.model.NetCafeUser;
import com.example.demo.model.PaymentRecord;
import com.example.demo.model.SettlementRequest;
import com.example.demo.repository.PaymentRecordRepository;
import com.example.demo.repository.SettlementRequestRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class SettlementService {

    @Autowired
    private SettlementRequestRepository settlementRequestRepository;

    @Autowired
    private PaymentRecordRepository paymentRecordRepository;

    @Autowired
    private PaymentService paymentService;

    /**
     * Calculate available balance for NetCafe user
     */
    public BigDecimal calculateAvailableBalance(NetCafeUser netCafeUser) {
        // Get all unsettled commission payments
        List<PaymentRecord> unsettledPayments = paymentRecordRepository
                .findByNetCafeUserAndIsSettledFalseOrderByPaymentDateDesc(netCafeUser);

        BigDecimal totalUnsettled = BigDecimal.ZERO;
        for (PaymentRecord payment : unsettledPayments) {
            if ("NETCAFE_COMMISSION".equals(payment.getPaymentType()) && "PENDING".equals(payment.getStatus())) {
                totalUnsettled = totalUnsettled.add(payment.getAmount());
            }
        }

        // Subtract any pending settlement requests
        BigDecimal pendingRequests = settlementRequestRepository
                .getTotalRequestedAmountByNetCafeUserAndStatus(netCafeUser, "PENDING");
        
        BigDecimal approvedRequests = settlementRequestRepository
                .getTotalRequestedAmountByNetCafeUserAndStatus(netCafeUser, "APPROVED");

        return totalUnsettled.subtract(pendingRequests).subtract(approvedRequests);
    }

    /**
     * Create a new settlement request
     */
    @Transactional
    public SettlementRequest createSettlementRequest(NetCafeUser netCafeUser, BigDecimal requestedAmount, 
                                                    String paymentMethod, String bankAccountNumber, 
                                                    String ifscCode, String bankName, String accountHolderName, 
                                                    String upiId) {
        
        BigDecimal availableBalance = calculateAvailableBalance(netCafeUser);
        
        if (requestedAmount.compareTo(availableBalance) > 0) {
            throw new RuntimeException("Requested amount exceeds available balance");
        }

        // Check if user has any pending requests
        long pendingCount = settlementRequestRepository.countPendingRequestsByNetCafeUser(netCafeUser);
        if (pendingCount > 0) {
            throw new RuntimeException("You already have a pending settlement request. Please wait for it to be processed.");
        }

        SettlementRequest request = new SettlementRequest();
        request.setNetCafeUser(netCafeUser);
        request.setRequestedAmount(requestedAmount);
        request.setAvailableAmount(availableBalance);
        request.setPaymentMethod(paymentMethod);

        if ("BANK_ACCOUNT".equals(paymentMethod)) {
            request.setBankAccountNumber(bankAccountNumber);
            request.setIfscCode(ifscCode);
            request.setBankName(bankName);
            request.setAccountHolderName(accountHolderName);
        } else if ("UPI".equals(paymentMethod)) {
            request.setUpiId(upiId);
        }

        return settlementRequestRepository.save(request);
    }

    /**
     * Get settlement requests for a NetCafe user
     */
    public List<SettlementRequest> getSettlementRequestsByNetCafeUser(NetCafeUser netCafeUser) {
        return settlementRequestRepository.findByNetCafeUserOrderByRequestDateDesc(netCafeUser);
    }

    /**
     * Get all pending settlement requests (for admin)
     */
    public List<SettlementRequest> getPendingSettlementRequests() {
        return settlementRequestRepository.findByStatusOrderByRequestDateAsc("PENDING");
    }

    /**
     * Get all settlement requests (for admin)
     */
    public List<SettlementRequest> getAllSettlementRequests() {
        return settlementRequestRepository.findAllByOrderByRequestDateDesc();
    }

    /**
     * Get settlement request by ID
     */
    public Optional<SettlementRequest> getSettlementRequestById(Long id) {
        return settlementRequestRepository.findById(id);
    }

    /**
     * Approve settlement request (admin action)
     */
    @Transactional
    public void approveSettlementRequest(Long requestId, String adminRemarks) {
        Optional<SettlementRequest> requestOpt = settlementRequestRepository.findById(requestId);
        
        if (requestOpt.isEmpty()) {
            throw new RuntimeException("Settlement request not found");
        }

        SettlementRequest request = requestOpt.get();
        
        if (!"PENDING".equals(request.getStatus())) {
            throw new RuntimeException("Settlement request is not in pending status");
        }

        request.setStatus("APPROVED");
        request.setAdminResponseDate(LocalDateTime.now());
        request.setAdminRemarks(adminRemarks);
        
        settlementRequestRepository.save(request);
    }

    /**
     * Reject settlement request (admin action)
     */
    @Transactional
    public void rejectSettlementRequest(Long requestId, String rejectionReason) {
        Optional<SettlementRequest> requestOpt = settlementRequestRepository.findById(requestId);
        
        if (requestOpt.isEmpty()) {
            throw new RuntimeException("Settlement request not found");
        }

        SettlementRequest request = requestOpt.get();
        
        if (!"PENDING".equals(request.getStatus())) {
            throw new RuntimeException("Settlement request is not in pending status");
        }

        request.setStatus("REJECTED");
        request.setAdminResponseDate(LocalDateTime.now());
        request.setRejectionReason(rejectionReason);
        
        settlementRequestRepository.save(request);
    }

    /**
     * Complete settlement request (admin action after payment)
     */
    @Transactional
    public void completeSettlementRequest(Long requestId, String transactionId) {
        Optional<SettlementRequest> requestOpt = settlementRequestRepository.findById(requestId);
        
        if (requestOpt.isEmpty()) {
            throw new RuntimeException("Settlement request not found");
        }

        SettlementRequest request = requestOpt.get();
        
        if (!"APPROVED".equals(request.getStatus())) {
            throw new RuntimeException("Settlement request is not approved");
        }

        // Mark the settlement request as completed
        request.setStatus("COMPLETED");
        request.setCompletionDate(LocalDateTime.now());
        request.setTransactionId(transactionId);
        settlementRequestRepository.save(request);

        // Settle the NetCafe commissions
        paymentService.settleNetCafeCommissions(request.getNetCafeUser(), transactionId, request.getPaymentMethod());
    }
}
