<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settlement Details - Admin Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/admin/dashboard}">
                                <i class="bi bi-house"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/admin/pending-payments}">
                                <i class="bi bi-credit-card"></i> Pending Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" th:href="@{/admin/settlements}">
                                <i class="bi bi-bank"></i> Settlements
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/admin/users}">
                                <i class="bi bi-people"></i> Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/admin/netcafe-users}">
                                <i class="bi bi-shop"></i> NetCafe Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/admin/logout}">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Settlement Request #<span th:text="${request.id}">001</span></h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a th:href="@{/admin/settlements}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Settlements
                        </a>
                    </div>
                </div>

                <!-- Flash Messages -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Request Information -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <i class="bi bi-info-circle"></i> Request Information
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Request ID:</strong> #<span th:text="${request.id}">001</span></p>
                                        <p><strong>NetCafe Name:</strong> <span th:text="${request.netCafeUser.name}">NetCafe Name</span></p>
                                        <p><strong>NetCafe Email:</strong> <span th:text="${request.netCafeUser.email}"><EMAIL></span></p>
                                        <p><strong>NetCafe Mobile:</strong> <span th:text="${request.netCafeUser.mobileNumber}">9876543210</span></p>
                                        <p><strong>Requested Amount:</strong> ₹<span th:text="${request.requestedAmount}">100.00</span></p>
                                        <p><strong>Available at Request:</strong> ₹<span th:text="${request.availableAmount}">150.00</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Current Available:</strong> ₹<span th:text="${currentBalance}">120.00</span></p>
                                        <p><strong>Status:</strong> 
                                            <span th:if="${request.status == 'PENDING'}" class="badge bg-warning">
                                                <i class="bi bi-clock"></i> Pending
                                            </span>
                                            <span th:if="${request.status == 'APPROVED'}" class="badge bg-info">
                                                <i class="bi bi-check-circle"></i> Approved
                                            </span>
                                            <span th:if="${request.status == 'COMPLETED'}" class="badge bg-success">
                                                <i class="bi bi-check-circle-fill"></i> Completed
                                            </span>
                                            <span th:if="${request.status == 'REJECTED'}" class="badge bg-danger">
                                                <i class="bi bi-x-circle"></i> Rejected
                                            </span>
                                        </p>
                                        <p><strong>Request Date:</strong> <span th:text="${#temporals.format(request.requestDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                        <p th:if="${request.adminResponseDate != null}"><strong>Response Date:</strong> <span th:text="${#temporals.format(request.adminResponseDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                        <p th:if="${request.completionDate != null}"><strong>Completion Date:</strong> <span th:text="${#temporals.format(request.completionDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Details -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <i class="bi bi-credit-card"></i> Payment Details
                            </div>
                            <div class="card-body">
                                <p><strong>Payment Method:</strong> 
                                    <span th:if="${request.paymentMethod == 'BANK_ACCOUNT'}" class="badge bg-info">
                                        <i class="bi bi-bank"></i> Bank Account
                                    </span>
                                    <span th:if="${request.paymentMethod == 'UPI'}" class="badge bg-success">
                                        <i class="bi bi-phone"></i> UPI
                                    </span>
                                </p>

                                <div th:if="${request.paymentMethod == 'BANK_ACCOUNT'}">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Account Holder Name:</strong> <span th:text="${request.accountHolderName}">John Doe</span></p>
                                            <p><strong>Account Number:</strong> <span th:text="${request.bankAccountNumber}">**********</span></p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>IFSC Code:</strong> <span th:text="${request.ifscCode}">SBIN0001234</span></p>
                                            <p><strong>Bank Name:</strong> <span th:text="${request.bankName}">State Bank of India</span></p>
                                        </div>
                                    </div>
                                </div>

                                <div th:if="${request.paymentMethod == 'UPI'}">
                                    <p><strong>UPI ID:</strong> <span th:text="${request.upiId}">user@paytm</span></p>
                                </div>

                                <div th:if="${request.transactionId != null}">
                                    <p><strong>Transaction ID:</strong> <span th:text="${request.transactionId}">TXN123456</span></p>
                                </div>
                            </div>
                        </div>

                        <!-- Admin Remarks/Rejection Reason -->
                        <div th:if="${request.adminRemarks != null || request.rejectionReason != null}" class="card mb-4">
                            <div class="card-header">
                                <i class="bi bi-chat-text"></i> Admin Notes
                            </div>
                            <div class="card-body">
                                <div th:if="${request.adminRemarks != null}">
                                    <h6>Admin Remarks</h6>
                                    <p th:text="${request.adminRemarks}">Admin remarks here</p>
                                </div>
                                <div th:if="${request.rejectionReason != null}">
                                    <h6>Rejection Reason</h6>
                                    <p class="text-danger" th:text="${request.rejectionReason}">Rejection reason here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Actions Panel -->
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="bi bi-gear"></i> Actions
                            </div>
                            <div class="card-body">
                                <!-- Pending Status Actions -->
                                <div th:if="${request.status == 'PENDING'}">
                                    <h6>Review Settlement Request</h6>
                                    <p class="text-muted">Review the request details and approve or reject.</p>
                                    
                                    <!-- Approve Form -->
                                    <form th:action="@{/admin/settlements/{id}/approve(id=${request.id})}" method="post" class="mb-3">
                                        <div class="mb-2">
                                            <label for="adminRemarks" class="form-label">Admin Remarks (Optional)</label>
                                            <textarea class="form-control" id="adminRemarks" name="adminRemarks" rows="2" placeholder="Add any remarks..."></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-success w-100" onclick="return confirm('Are you sure you want to approve this settlement request?')">
                                            <i class="bi bi-check-circle"></i> Approve Request
                                        </button>
                                    </form>

                                    <!-- Reject Form -->
                                    <form th:action="@{/admin/settlements/{id}/reject(id=${request.id})}" method="post">
                                        <div class="mb-2">
                                            <label for="rejectionReason" class="form-label">Rejection Reason *</label>
                                            <textarea class="form-control" id="rejectionReason" name="rejectionReason" rows="2" placeholder="Reason for rejection..." required></textarea>
                                        </div>
                                        <button type="submit" class="btn btn-danger w-100" onclick="return confirm('Are you sure you want to reject this settlement request?')">
                                            <i class="bi bi-x-circle"></i> Reject Request
                                        </button>
                                    </form>
                                </div>

                                <!-- Approved Status Actions -->
                                <div th:if="${request.status == 'APPROVED'}">
                                    <h6>Complete Settlement</h6>
                                    <p class="text-muted">Enter transaction ID after making the payment.</p>
                                    
                                    <form th:action="@{/admin/settlements/{id}/complete(id=${request.id})}" method="post">
                                        <div class="mb-2">
                                            <label for="transactionId" class="form-label">Transaction ID *</label>
                                            <input type="text" class="form-control" id="transactionId" name="transactionId" placeholder="Enter transaction ID" required>
                                        </div>
                                        <button type="submit" class="btn btn-success w-100" onclick="return confirm('Are you sure you want to mark this settlement as completed?')">
                                            <i class="bi bi-check-circle-fill"></i> Mark as Completed
                                        </button>
                                    </form>
                                </div>

                                <!-- Completed/Rejected Status -->
                                <div th:if="${request.status == 'COMPLETED' || request.status == 'REJECTED'}">
                                    <h6>Settlement <span th:text="${request.status}">Status</span></h6>
                                    <p class="text-muted">This settlement request has been processed.</p>
                                    <div th:if="${request.status == 'COMPLETED'}" class="alert alert-success">
                                        <i class="bi bi-check-circle-fill"></i> Settlement completed successfully
                                    </div>
                                    <div th:if="${request.status == 'REJECTED'}" class="alert alert-danger">
                                        <i class="bi bi-x-circle"></i> Settlement request was rejected
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
