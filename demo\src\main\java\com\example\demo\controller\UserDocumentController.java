package com.example.demo.controller;

import com.example.demo.model.Document;
import com.example.demo.model.GeneralUser;
import com.example.demo.model.UserDocument;
import com.example.demo.service.DocumentService;
import com.example.demo.service.UserDocumentService;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Controller
public class UserDocumentController {

    @Autowired
    private UserDocumentService userDocumentService;

    @Autowired
    private DocumentService documentService;

    // User Document Management
    @GetMapping("/user/documents")
    public String userDocuments(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        if (user == null) {
            return "redirect:/user/login";
        }

        List<UserDocument> userDocuments = userDocumentService.getUserDocuments(user);
        List<Document> availableDocuments = documentService.getAllDocuments();

        model.addAttribute("user", user);
        model.addAttribute("userDocuments", userDocuments);
        model.addAttribute("availableDocuments", availableDocuments);

        return "user/documents/index";
    }

    @GetMapping("/user/documents/upload")
    public String showUploadForm(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        if (user == null) {
            return "redirect:/user/login";
        }

        List<Document> availableDocuments = documentService.getAllDocuments();

        model.addAttribute("user", user);
        model.addAttribute("availableDocuments", availableDocuments);

        return "user/documents/upload";
    }

    @PostMapping("/user/documents/upload")
    public String uploadDocument(
            @RequestParam("documentId") Long documentId,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "description", required = false) String description,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        if (user == null) {
            return "redirect:/user/login";
        }

        try {
            if (file.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Please select a file to upload");
                return "redirect:/user/documents/upload";
            }

            userDocumentService.saveUserDocument(user, documentId, file, description);
            redirectAttributes.addFlashAttribute("success", "Document uploaded successfully");
            return "redirect:/user/documents";
        } catch (IOException e) {
            redirectAttributes.addFlashAttribute("error", "Error uploading document: " + e.getMessage());
            return "redirect:/user/documents/upload";
        }
    }

    @GetMapping("/user/documents/{id}/download")
    public ResponseEntity<byte[]> downloadDocument(@PathVariable Long id, HttpSession session) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        if (user == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }

        Optional<UserDocument> userDocumentOpt = userDocumentService.getUserDocumentById(id);
        if (userDocumentOpt.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        UserDocument userDocument = userDocumentOpt.get();
        // Check if the document belongs to the user
        if (!userDocument.getUser().getId().equals(user.getId())) {
            return new ResponseEntity<>(HttpStatus.FORBIDDEN);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(userDocument.getDocumentContentType()));
        headers.setContentDispositionFormData("attachment", userDocument.getDocumentName());

        return new ResponseEntity<>(userDocument.getDocumentFile(), headers, HttpStatus.OK);
    }

    @PostMapping("/user/documents/{id}/delete")
    public String deleteDocument(@PathVariable Long id, HttpSession session, RedirectAttributes redirectAttributes) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        if (user == null) {
            return "redirect:/user/login";
        }

        Optional<UserDocument> userDocumentOpt = userDocumentService.getUserDocumentById(id);
        if (userDocumentOpt.isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "Document not found");
            return "redirect:/user/documents";
        }

        UserDocument userDocument = userDocumentOpt.get();
        // Check if the document belongs to the user
        if (!userDocument.getUser().getId().equals(user.getId())) {
            redirectAttributes.addFlashAttribute("error", "You don't have permission to delete this document");
            return "redirect:/user/documents";
        }

        userDocumentService.deleteUserDocument(id);
        redirectAttributes.addFlashAttribute("success", "Document deleted successfully");
        return "redirect:/user/documents";
    }

    // REST API for fetching user documents
    @GetMapping("/api/user/documents")
    @ResponseBody
    public ResponseEntity<?> getUserDocumentsApi(HttpSession session) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        if (user == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("User not logged in");
        }

        List<UserDocument> userDocuments = userDocumentService.getUserDocuments(user);

        // Create a simplified response with only necessary information
        List<Map<String, Object>> response = new ArrayList<>();
        for (UserDocument doc : userDocuments) {
            Map<String, Object> docInfo = new HashMap<>();
            docInfo.put("id", doc.getId());
            docInfo.put("documentId", doc.getDocument().getId());
            docInfo.put("documentName", doc.getDocument().getName());
            docInfo.put("fileName", doc.getDocumentName());
            docInfo.put("uploadDate", doc.getUploadDate().toString());
            docInfo.put("isVerified", doc.getIsVerified());

            response.add(docInfo);
        }

        return ResponseEntity.ok(response);
    }

    @GetMapping("/api/user/documents/by-type/{documentId}")
    @ResponseBody
    public ResponseEntity<?> getUserDocumentByType(@PathVariable Long documentId, HttpSession session) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        if (user == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("User not logged in");
        }

        Optional<Document> documentOpt = documentService.getDocumentById(documentId);
        if (documentOpt.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("Document type not found");
        }

        Optional<UserDocument> userDocumentOpt = userDocumentService.getUserDocumentByUserAndDocument(user, documentOpt.get());
        if (userDocumentOpt.isEmpty()) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body("User document not found");
        }

        UserDocument doc = userDocumentOpt.get();
        Map<String, Object> response = new HashMap<>();
        response.put("id", doc.getId());
        response.put("documentId", doc.getDocument().getId());
        response.put("documentName", doc.getDocument().getName());
        response.put("fileName", doc.getDocumentName());
        response.put("uploadDate", doc.getUploadDate().toString());
        response.put("isVerified", doc.getIsVerified());
        response.put("downloadUrl", "/user/documents/" + doc.getId() + "/download");

        return ResponseEntity.ok(response);
    }

    // Admin Document Verification
    @GetMapping("/admin/user-documents")
    public String adminUserDocuments(HttpSession session, Model model) {
        if (session.getAttribute("admin") == null) {
            return "redirect:/admin/login";
        }

        List<UserDocument> unverifiedDocuments = userDocumentService.getUnverifiedDocuments();
        model.addAttribute("unverifiedDocuments", unverifiedDocuments);

        return "admin/user-documents/index";
    }

    @GetMapping("/admin/user-documents/{id}")
    public String viewUserDocument(@PathVariable Long id, HttpSession session, Model model) {
        if (session.getAttribute("admin") == null) {
            return "redirect:/admin/login";
        }

        Optional<UserDocument> userDocumentOpt = userDocumentService.getUserDocumentById(id);
        if (userDocumentOpt.isEmpty()) {
            return "redirect:/admin/user-documents";
        }

        model.addAttribute("userDocument", userDocumentOpt.get());
        return "admin/user-documents/view";
    }

    @GetMapping("/admin/user-documents/{id}/download")
    public ResponseEntity<byte[]> adminDownloadDocument(@PathVariable Long id, HttpSession session) {
        if (session.getAttribute("admin") == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }

        Optional<UserDocument> userDocumentOpt = userDocumentService.getUserDocumentById(id);
        if (userDocumentOpt.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        UserDocument userDocument = userDocumentOpt.get();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(userDocument.getDocumentContentType()));
        headers.setContentDispositionFormData("attachment", userDocument.getDocumentName());

        return new ResponseEntity<>(userDocument.getDocumentFile(), headers, HttpStatus.OK);
    }

    @PostMapping("/admin/user-documents/{id}/verify")
    public String verifyUserDocument(
            @PathVariable Long id,
            @RequestParam("isVerified") Boolean isVerified,
            @RequestParam("remarks") String remarks,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        if (session.getAttribute("admin") == null) {
            return "redirect:/admin/login";
        }

        try {
            userDocumentService.verifyUserDocument(id, isVerified, remarks);
            redirectAttributes.addFlashAttribute("success", "Document verification status updated successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error updating verification status: " + e.getMessage());
        }

        return "redirect:/admin/user-documents";
    }
}
