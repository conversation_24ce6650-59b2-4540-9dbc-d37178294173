<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Document - User</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #28a745;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .upload-card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            padding: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/bonds}">
                            <i class="bi bi-scissors"></i> My Bonds
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/status}">
                            <i class="bi bi-graph-up"></i> Status Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/user/documents}">
                            <i class="bi bi-file-earmark"></i> My Documents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 content">
                <div class="header-card">
                    <h2><i class="bi bi-upload"></i> Upload Document</h2>
                    <p>Upload a new document to your profile</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <!-- Back Button -->
                <div class="mb-4">
                    <a th:href="@{/user/documents}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Documents
                    </a>
                </div>

                <!-- Upload Form -->
                <div class="row justify-content-center">
                    <div class="col-md-8">
                        <div class="card upload-card">
                            <div class="card-body">
                                <h4 class="card-title mb-4">Upload New Document</h4>
                                
                                <form th:action="@{/user/documents/upload}" method="post" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="documentId" class="form-label">Document Type</label>
                                        <select class="form-select" id="documentId" name="documentId" required>
                                            <option value="">Select Document Type</option>
                                            <option th:each="doc : ${availableDocuments}" 
                                                    th:value="${doc.id}" 
                                                    th:text="${doc.name}"
                                                    th:selected="${param.documentId != null && param.documentId[0] == doc.id.toString()}">
                                                Document Type
                                            </option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="file" class="form-label">Document File</label>
                                        <input type="file" class="form-control" id="file" name="file" required>
                                        <div class="form-text">Supported formats: PDF, JPG, PNG, JPEG. Maximum size: 10MB</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description (Optional)</label>
                                        <textarea class="form-control" id="description" name="description" rows="3" placeholder="Add a description for this document"></textarea>
                                    </div>
                                    
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> Note: If you already have a document of the selected type, it will be replaced with this new upload.
                                    </div>
                                    
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-success">
                                            <i class="bi bi-upload"></i> Upload Document
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
