<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View User Document - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #007bff;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .document-card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .document-preview {
            max-height: 500px;
            overflow: auto;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 20px;
        }
        .document-preview img {
            max-width: 100%;
            height: auto;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes/applications}">
                            <i class="bi bi-file-earmark-text"></i> Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes}">
                            <i class="bi bi-list-check"></i> Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/bonds}">
                            <i class="bi bi-scissors"></i> Bond Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/status}">
                            <i class="bi bi-graph-up"></i> Status Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/documents}">
                            <i class="bi bi-file-earmark"></i> Documents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/user-documents}">
                            <i class="bi bi-file-earmark-check"></i> User Documents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 content">
                <div class="header-card">
                    <h2><i class="bi bi-file-earmark-check"></i> View User Document</h2>
                    <p>Review and verify user document</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <!-- Back Button -->
                <div class="mb-4">
                    <a th:href="@{/admin/user-documents}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to User Documents
                    </a>
                </div>

                <!-- Document Details -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card document-card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Document Details</h5>
                            </div>
                            <div class="card-body">
                                <table class="table">
                                    <tr>
                                        <th>Document ID:</th>
                                        <td th:text="${userDocument.id}">1</td>
                                    </tr>
                                    <tr>
                                        <th>Document Type:</th>
                                        <td th:text="${userDocument.document.name}">Document Type</td>
                                    </tr>
                                    <tr>
                                        <th>File Name:</th>
                                        <td th:text="${userDocument.documentName}">document.pdf</td>
                                    </tr>
                                    <tr>
                                        <th>User:</th>
                                        <td th:text="${userDocument.user.name}">User Name</td>
                                    </tr>
                                    <tr>
                                        <th>Upload Date:</th>
                                        <td th:text="${#temporals.format(userDocument.uploadDate, 'dd-MM-yyyy HH:mm')}">01-01-2023 12:00</td>
                                    </tr>
                                    <tr th:if="${userDocument.description}">
                                        <th>Description:</th>
                                        <td th:text="${userDocument.description}">Description</td>
                                    </tr>
                                    <tr>
                                        <th>Verification Status:</th>
                                        <td>
                                            <span th:if="${userDocument.isVerified}" class="badge bg-success">Verified</span>
                                            <span th:unless="${userDocument.isVerified}" class="badge bg-warning">Pending Verification</span>
                                        </td>
                                    </tr>
                                </table>

                                <div class="mt-3">
                                    <a th:href="@{'/admin/user-documents/' + ${userDocument.id} + '/download'}" class="btn btn-primary">
                                        <i class="bi bi-download"></i> Download Document
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card document-card">
                            <div class="card-header">
                                <h5 class="mb-0">Verification</h5>
                            </div>
                            <div class="card-body">
                                <form th:action="@{'/admin/user-documents/' + ${userDocument.id} + '/verify'}" method="post">
                                    <div class="mb-3">
                                        <label class="form-label">Verification Status</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="isVerified" id="verified" value="true" required>
                                            <label class="form-check-label" for="verified">
                                                <span class="badge bg-success">Verify</span> - Document is valid and authentic
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="isVerified" id="rejected" value="false" required>
                                            <label class="form-check-label" for="rejected">
                                                <span class="badge bg-danger">Reject</span> - Document is invalid or suspicious
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="remarks" class="form-label">Remarks</label>
                                        <textarea class="form-control" id="remarks" name="remarks" rows="3" required placeholder="Add verification remarks"></textarea>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-success">
                                            <i class="bi bi-check-circle"></i> Submit Verification
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Document Preview (for images) -->
                <div class="card mt-4" th:if="${userDocument.documentContentType != null && userDocument.documentContentType.startsWith('image/')}">
                    <div class="card-header">
                        <h5 class="mb-0">Document Preview</h5>
                    </div>
                    <div class="card-body">
                        <div class="document-preview">
                            <p>Image preview is available after downloading the document.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
