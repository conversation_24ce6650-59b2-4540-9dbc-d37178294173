-- Database schema fix for NetCafe Application
-- This script fixes the column name mismatch in netcafe_applications table

-- Check if the table exists and has the wrong column name
-- If net_cafe_id exists, rename it to netcafe_user_id

-- First, let's check the current structure
-- SHOW COLUMNS FROM netcafe_applications;

-- Option 1: If the table has net_cafe_id column, rename it
-- ALTER TABLE netcafe_applications CHANGE COLUMN net_cafe_id netcafe_user_id BIGINT NOT NULL;

-- Option 2: If the table doesn't exist or needs to be recreated, create it with correct structure
CREATE TABLE IF NOT EXISTS netcafe_applications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    netcafe_user_id BIGINT NOT NULL,
    application_id BIGINT NOT NULL,
    assignment_date DATETIME NOT NULL,
    status VARCHAR(50),
    remarks TEXT,
    completion_date DATETIME,
    confirmation_date DATETIME,
    bond_broken BOOLEAN DEFAULT FALSE,
    bond_broken_date DATETIME,
    bond_break_reason TEXT,
    bond_break_initiator <PERSON><PERSON><PERSON><PERSON>(50),
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (netcafe_user_id) REFERENCES netcafe_users(id),
    FOREIGN KEY (application_id) REFERENCES scheme_applications(id)
);

-- If you need to rename the column (run this if net_cafe_id exists):
-- ALTER TABLE netcafe_applications CHANGE COLUMN net_cafe_id netcafe_user_id BIGINT NOT NULL;

-- Add index for better performance
CREATE INDEX IF NOT EXISTS idx_netcafe_applications_netcafe_user ON netcafe_applications(netcafe_user_id);
CREATE INDEX IF NOT EXISTS idx_netcafe_applications_application ON netcafe_applications(application_id);
CREATE INDEX IF NOT EXISTS idx_netcafe_applications_status ON netcafe_applications(status);
