<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #28a745;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .profile-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .profile-header {
            background-color: #28a745;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .profile-img {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 5px solid white;
            margin: 0 auto 15px;
            object-fit: cover;
            background-color: #f8f9fa;
        }
        .profile-body {
            padding: 20px;
        }
        .info-label {
            font-weight: bold;
            color: #6c757d;
        }
        .section-title {
            color: #28a745;
            border-bottom: 2px solid #28a745;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Available Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" href="#">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2>My Profile</h2>
                    <p>View and manage your personal information</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="profile-card">
                            <div class="profile-header">
                                <div class="profile-img">
                                    <i class="bi bi-person-fill" style="font-size: 60px;"></i>
                                </div>
                                <h4 th:text="${user.name}">John Doe</h4>
                                <p th:text="${user.email}"><EMAIL></p>
                            </div>
                            <div class="profile-body">
                                <p><i class="bi bi-telephone me-2"></i> <span th:text="${user.mobileNumber}">1234567890</span></p>
                                <p><i class="bi bi-geo-alt me-2"></i> <span th:text="${user.address}">123 Main St, City</span></p>
                                <div class="d-grid gap-2 mt-3">
                                    <a th:href="@{/user/edit-profile}" class="btn btn-outline-success">
                                        <i class="bi bi-pencil"></i> Edit Profile
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                Personal Information
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <p class="info-label">Full Name</p>
                                    </div>
                                    <div class="col-md-8">
                                        <p th:text="${user.name}">John Doe</p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <p class="info-label">Email Address</p>
                                    </div>
                                    <div class="col-md-8">
                                        <p th:text="${user.email}"><EMAIL></p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <p class="info-label">Mobile Number</p>
                                    </div>
                                    <div class="col-md-8">
                                        <p th:text="${user.mobileNumber}">1234567890</p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <p class="info-label">Address</p>
                                    </div>
                                    <div class="col-md-8">
                                        <p th:text="${user.address}">123 Main St, City, State, ZIP</p>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-4">
                                        <p class="info-label">Registration Date</p>
                                    </div>
                                    <div class="col-md-8">
                                        <p th:text="${#temporals.format(user.registrationDate, 'dd-MM-yyyy')}">01-01-2023</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                Account Security
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a th:href="@{/user/change-password}" class="btn btn-outline-primary">
                                        <i class="bi bi-key"></i> Change Password
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <a th:href="@{/user/dashboard}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
