package com.example.demo.config;

import com.example.demo.service.AdminService;
import com.example.demo.service.DocumentService;
import com.example.demo.service.GeneralUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private AdminService adminService;

    @Autowired
    private GeneralUserService generalUserService;

    @Autowired
    private DocumentService documentService;

    @Override
    public void run(String... args) throws Exception {
        // Initialize default admin user
        adminService.initializeDefaultAdmin();

        // Initialize default general user
        generalUserService.initializeDefaultUser();

        // Initialize default documents
        documentService.initializeDefaultDocuments();
    }
}
