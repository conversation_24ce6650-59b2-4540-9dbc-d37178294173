package com.example.demo.service;

import com.example.demo.model.GeneralUser;
import com.example.demo.repository.GeneralUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class GeneralUserService {

    @Autowired
    private GeneralUserRepository generalUserRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    public GeneralUser registerUser(GeneralUser user) {
        // Encode password
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // Save user
        return generalUserRepository.save(user);
    }

    public Optional<GeneralUser> findByEmail(String email) {
        return generalUserRepository.findByEmail(email);
    }

    public boolean authenticateUser(String email, String password) {
        Optional<GeneralUser> userOpt = generalUserRepository.findByEmail(email);

        if (userOpt.isPresent()) {
            GeneralUser user = userOpt.get();
            return passwordEncoder.matches(password, user.getPassword()) && user.isActive();
        }

        return false;
    }

    public GeneralUser updateUser(GeneralUser user) {
        return generalUserRepository.save(user);
    }

    public Optional<GeneralUser> findById(Long id) {
        return generalUserRepository.findById(id);
    }

    public boolean isEmailTaken(String email) {
        return generalUserRepository.existsByEmail(email);
    }

    // Initialize a default user for testing if none exists
    public void initializeDefaultUser() {
        if (generalUserRepository.count() == 0) {
            GeneralUser user = new GeneralUser();
            user.setName("Test User");
            user.setEmail("<EMAIL>");
            user.setPassword(passwordEncoder.encode("password"));
            user.setMobileNumber("9876543210");
            user.setAddress("123 Test Street, Test City");
            generalUserRepository.save(user);
        }
    }

    public GeneralUser updateUserProfile(Long userId, GeneralUser updatedUser) {
        Optional<GeneralUser> existingUserOpt = generalUserRepository.findById(userId);

        if (existingUserOpt.isPresent()) {
            GeneralUser existingUser = existingUserOpt.get();

            // Update fields that are allowed to be changed
            existingUser.setName(updatedUser.getName());
            existingUser.setMobileNumber(updatedUser.getMobileNumber());
            existingUser.setAddress(updatedUser.getAddress());

            // Save and return updated user
            return generalUserRepository.save(existingUser);
        }

        throw new RuntimeException("User not found");
    }

    public void updatePassword(Long userId, String newPassword) {
        Optional<GeneralUser> userOpt = generalUserRepository.findById(userId);

        if (userOpt.isPresent()) {
            GeneralUser user = userOpt.get();
            user.setPassword(passwordEncoder.encode(newPassword));
            generalUserRepository.save(user);
        } else {
            throw new RuntimeException("User not found");
        }
    }

    public long countAllUsers() {
        return generalUserRepository.count();
    }

    public long countActiveUsers() {
        return generalUserRepository.countByActiveTrue();
    }

    public List<GeneralUser> getAllUsers() {
        return generalUserRepository.findAll();
    }

    public Optional<GeneralUser> getUserById(Long id) {
        return generalUserRepository.findById(id);
    }

    public void deleteUser(Long userId) {
        Optional<GeneralUser> userOpt = generalUserRepository.findById(userId);

        if (userOpt.isPresent()) {
            GeneralUser user = userOpt.get();
            user.setActive(false); // Soft delete
            generalUserRepository.save(user);
        } else {
            throw new RuntimeException("User not found");
        }
    }

    public GeneralUser toggleUserStatus(Long userId) {
        Optional<GeneralUser> userOpt = generalUserRepository.findById(userId);

        if (userOpt.isPresent()) {
            GeneralUser user = userOpt.get();
            user.setActive(!user.isActive()); // Toggle active status
            return generalUserRepository.save(user);
        } else {
            throw new RuntimeException("User not found");
        }
    }
}
