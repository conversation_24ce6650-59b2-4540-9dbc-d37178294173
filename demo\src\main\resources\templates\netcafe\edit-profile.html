<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Profile - NetCafe</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #495057;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .welcome-card {
            background-color: #007bff;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .document-img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 10px;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>NetCafe Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" href="#">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" href="#">
                            <i class="bi bi-file-earmark-text"></i> Documents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" href="#">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="welcome-card">
                    <h2>Edit Your Profile</h2>
                    <p>Update your personal information and documents</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-header">
                        Personal Information
                    </div>
                    <div class="card-body">
                        <form th:action="@{/netcafe/update-profile}" method="post" enctype="multipart/form-data" th:object="${user}">
                            <input type="hidden" th:field="*{id}">
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="name" th:field="*{name}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" th:field="*{email}" readonly>
                                    <small class="text-muted">Email cannot be changed</small>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="mobileNumber" class="form-label">Mobile Number</label>
                                    <input type="text" class="form-control" id="mobileNumber" th:field="*{mobileNumber}" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="password" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="password" name="newPassword">
                                    <small class="text-muted">Leave blank to keep current password</small>
                                </div>
                            </div>
                            
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="aadharNumber" class="form-label">Aadhar Number</label>
                                    <input type="text" class="form-control" id="aadharNumber" th:field="*{aadharNumber}" readonly>
                                    <small class="text-muted">Aadhar number cannot be changed</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="panNumber" class="form-label">PAN Number</label>
                                    <input type="text" class="form-control" id="panNumber" th:field="*{panNumber}" readonly>
                                    <small class="text-muted">PAN number cannot be changed</small>
                                </div>
                            </div>
                            
                            <div class="card mb-4">
                                <div class="card-header">
                                    Update Documents
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="photoFile" class="form-label">Your Photo</label>
                                            <input type="file" class="form-control" id="photoFile" name="photoFile">
                                            <small class="text-muted">Leave blank to keep current photo</small>
                                            <div class="mt-2">
                                                <img th:src="@{/documents/photo/{id}(id=${user.id})}" alt="Current Photo" class="document-img" style="max-height: 100px;">
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="cscCertificateFile" class="form-label">CSC Certificate</label>
                                            <input type="file" class="form-control" id="cscCertificateFile" name="cscCertificateFile">
                                            <small class="text-muted">Leave blank to keep current certificate</small>
                                            <div class="mt-2">
                                                <img th:src="@{/documents/csc-certificate/{id}(id=${user.id})}" alt="Current CSC Certificate" class="document-img" style="max-height: 100px;">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="aadharCardPhotoFile" class="form-label">Aadhar Card Photo</label>
                                            <input type="file" class="form-control" id="aadharCardPhotoFile" name="aadharCardPhotoFile">
                                            <small class="text-muted">Leave blank to keep current Aadhar card photo</small>
                                            <div class="mt-2" th:if="${user.aadharCardPhoto != null}">
                                                <img th:src="@{/documents/aadhar-card/{id}(id=${user.id})}" alt="Current Aadhar Card" class="document-img" style="max-height: 100px;">
                                            </div>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label for="panCardPhotoFile" class="form-label">PAN Card Photo</label>
                                            <input type="file" class="form-control" id="panCardPhotoFile" name="panCardPhotoFile">
                                            <small class="text-muted">Leave blank to keep current PAN card photo</small>
                                            <div class="mt-2" th:if="${user.panCardPhoto != null}">
                                                <img th:src="@{/documents/pan-card/{id}(id=${user.id})}" alt="Current PAN Card" class="document-img" style="max-height: 100px;">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">Update Profile</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
