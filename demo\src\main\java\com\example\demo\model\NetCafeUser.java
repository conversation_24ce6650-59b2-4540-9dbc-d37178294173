package com.example.demo.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "netcafe_users")
public class NetCafeUser {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String name;

    @Column(nullable = false, unique = true)
    private String email;

    @Column(nullable = false)
    private String password;

    @Column(nullable = false)
    private String mobileNumber;

    @Column
    private String shopName;

    @Column(nullable = false, unique = true)
    private String aadharNumber;

    @Column(nullable = false, unique = true)
    private String panNumber;

    @Lob
    @Column(nullable = false, columnDefinition = "LONGBLOB")
    private byte[] cscCertificate;

    @Lob
    @Column(nullable = false, columnDefinition = "LONGBLOB")
    private byte[] photo;

    @Lob
    @Column(columnDefinition = "LONGBLOB")
    private byte[] aadharCardPhoto;

    @Lob
    @Column(columnDefinition = "LONGBLOB")
    private byte[] panCardPhoto;

    @Column
    private String photoContentType;

    @Column
    private String cscCertificateContentType;

    @Column
    private String aadharCardPhotoContentType;

    @Column
    private String panCardPhotoContentType;

    @Column(nullable = false)
    private boolean approved = false;

    @Column(nullable = false)
    private boolean active = true;

    @Column(nullable = false)
    private LocalDateTime registrationDate;

    // Default constructor
    public NetCafeUser() {
        this.registrationDate = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getAadharNumber() {
        return aadharNumber;
    }

    public void setAadharNumber(String aadharNumber) {
        this.aadharNumber = aadharNumber;
    }

    public String getPanNumber() {
        return panNumber;
    }

    public void setPanNumber(String panNumber) {
        this.panNumber = panNumber;
    }

    public byte[] getCscCertificate() {
        return cscCertificate;
    }

    public void setCscCertificate(byte[] cscCertificate) {
        this.cscCertificate = cscCertificate;
    }

    public byte[] getPhoto() {
        return photo;
    }

    public void setPhoto(byte[] photo) {
        this.photo = photo;
    }

    public byte[] getAadharCardPhoto() {
        return aadharCardPhoto;
    }

    public void setAadharCardPhoto(byte[] aadharCardPhoto) {
        this.aadharCardPhoto = aadharCardPhoto;
    }

    public byte[] getPanCardPhoto() {
        return panCardPhoto;
    }

    public void setPanCardPhoto(byte[] panCardPhoto) {
        this.panCardPhoto = panCardPhoto;
    }

    public String getPhotoContentType() {
        return photoContentType;
    }

    public void setPhotoContentType(String photoContentType) {
        this.photoContentType = photoContentType;
    }

    public String getCscCertificateContentType() {
        return cscCertificateContentType;
    }

    public void setCscCertificateContentType(String cscCertificateContentType) {
        this.cscCertificateContentType = cscCertificateContentType;
    }

    public String getAadharCardPhotoContentType() {
        return aadharCardPhotoContentType;
    }

    public void setAadharCardPhotoContentType(String aadharCardPhotoContentType) {
        this.aadharCardPhotoContentType = aadharCardPhotoContentType;
    }

    public String getPanCardPhotoContentType() {
        return panCardPhotoContentType;
    }

    public void setPanCardPhotoContentType(String panCardPhotoContentType) {
        this.panCardPhotoContentType = panCardPhotoContentType;
    }

    public boolean isApproved() {
        return approved;
    }

    public void setApproved(boolean approved) {
        this.approved = approved;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public LocalDateTime getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(LocalDateTime registrationDate) {
        this.registrationDate = registrationDate;
    }
}
