package com.example.demo.repository;

import com.example.demo.model.NetCafeCommission;
import com.example.demo.model.Scheme;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface NetCafeCommissionRepository extends JpaRepository<NetCafeCommission, Long> {

    // Find active commission for a scheme
    Optional<NetCafeCommission> findBySchemeAndIsActiveTrue(Scheme scheme);
    
    // Find all active commissions
    List<NetCafeCommission> findByIsActiveTrueOrderByScheme_NameAsc();
    
    // Find all commissions for a scheme
    List<NetCafeCommission> findBySchemeOrderByCreatedAtDesc(Scheme scheme);
    
    // Find commission by scheme ID
    @Query("SELECT nc FROM NetCafeCommission nc WHERE nc.scheme.id = ?1 AND nc.isActive = true")
    Optional<NetCafeCommission> findActiveCommissionBySchemeId(Long schemeId);
}
