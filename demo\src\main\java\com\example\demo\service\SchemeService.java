package com.example.demo.service;

import com.example.demo.model.Document;
import com.example.demo.model.Scheme;
import com.example.demo.model.SchemeDocument;
import com.example.demo.repository.DocumentRepository;
import com.example.demo.repository.SchemeDocumentRepository;
import com.example.demo.repository.SchemeRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
public class SchemeService {

    @Autowired
    private SchemeRepository schemeRepository;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private SchemeDocumentRepository schemeDocumentRepository;

    public List<Scheme> getAllSchemes() {
        return schemeRepository.findAll();
    }

    public List<Scheme> getAllActiveSchemes() {
        return schemeRepository.findByActiveTrue();
    }

    public Optional<Scheme> getSchemeById(Long id) {
        return schemeRepository.findById(id);
    }

    @Transactional
    public Scheme createScheme(Scheme scheme, MultipartFile document, List<Long> documentIds) throws IOException {
        if (document != null && !document.isEmpty()) {
            scheme.setSchemeDocument(document.getBytes());
            scheme.setDocumentContentType(document.getContentType());
        }

        // Save the scheme first to get an ID
        Scheme savedScheme = schemeRepository.save(scheme);

        // Add document requirements if provided
        if (documentIds != null && !documentIds.isEmpty()) {
            for (Long documentId : documentIds) {
                Optional<Document> documentOpt = documentRepository.findById(documentId);
                if (documentOpt.isPresent()) {
                    savedScheme.addDocumentRequirement(documentOpt.get(), true);
                }
            }
            // Save again with document requirements
            savedScheme = schemeRepository.save(savedScheme);
        }

        return savedScheme;
    }

    @Transactional
    public Scheme updateScheme(Long id, Scheme updatedScheme, MultipartFile document, List<Long> documentIds) throws IOException {
        Optional<Scheme> existingSchemeOpt = schemeRepository.findById(id);

        if (existingSchemeOpt.isPresent()) {
            Scheme existingScheme = existingSchemeOpt.get();

            existingScheme.setName(updatedScheme.getName());
            existingScheme.setDescription(updatedScheme.getDescription());
            existingScheme.setEligibility(updatedScheme.getEligibility());
            existingScheme.setBenefits(updatedScheme.getBenefits());
            existingScheme.setApplicationProcess(updatedScheme.getApplicationProcess());
            existingScheme.setRequiredDocuments(updatedScheme.getRequiredDocuments());
            existingScheme.setActive(updatedScheme.isActive());

            if (document != null && !document.isEmpty()) {
                existingScheme.setSchemeDocument(document.getBytes());
                existingScheme.setDocumentContentType(document.getContentType());
            }

            // Update document requirements if provided
            if (documentIds != null) {
                // Clear existing document requirements
                existingScheme.getSchemeDocuments().clear();

                // Add new document requirements
                for (Long documentId : documentIds) {
                    Optional<Document> documentOpt = documentRepository.findById(documentId);
                    if (documentOpt.isPresent()) {
                        existingScheme.addDocumentRequirement(documentOpt.get(), true);
                    }
                }
            }

            return schemeRepository.save(existingScheme);
        }

        throw new RuntimeException("Scheme not found with ID: " + id);
    }

    // Backward compatibility method for existing code
    @Transactional
    public Scheme createScheme(Scheme scheme, MultipartFile document) throws IOException {
        return createScheme(scheme, document, null);
    }

    // Backward compatibility method for existing code
    @Transactional
    public Scheme updateScheme(Long id, Scheme updatedScheme, MultipartFile document) throws IOException {
        return updateScheme(id, updatedScheme, document, null);
    }

    public void deleteScheme(Long id) {
        schemeRepository.deleteById(id);
    }

    public void deactivateScheme(Long id) {
        Optional<Scheme> schemeOpt = schemeRepository.findById(id);

        if (schemeOpt.isPresent()) {
            Scheme scheme = schemeOpt.get();
            scheme.setActive(false);
            schemeRepository.save(scheme);
        } else {
            throw new RuntimeException("Scheme not found with ID: " + id);
        }
    }

    public void activateScheme(Long id) {
        Optional<Scheme> schemeOpt = schemeRepository.findById(id);

        if (schemeOpt.isPresent()) {
            Scheme scheme = schemeOpt.get();
            scheme.setActive(true);
            schemeRepository.save(scheme);
        } else {
            throw new RuntimeException("Scheme not found with ID: " + id);
        }
    }

    // Get all documents required for a scheme
    public List<Document> getRequiredDocumentsForScheme(Long schemeId) {
        Optional<Scheme> schemeOpt = schemeRepository.findById(schemeId);
        if (schemeOpt.isPresent()) {
            Scheme scheme = schemeOpt.get();
            List<SchemeDocument> schemeDocuments = schemeDocumentRepository.findBySchemeAndRequired(scheme, true);
            return schemeDocuments.stream()
                    .map(SchemeDocument::getDocument)
                    .toList();
        }
        return List.of();
    }

    // Check if a document is required for a scheme
    public boolean isDocumentRequiredForScheme(Long schemeId, Long documentId) {
        Optional<Scheme> schemeOpt = schemeRepository.findById(schemeId);
        Optional<Document> documentOpt = documentRepository.findById(documentId);

        if (schemeOpt.isPresent() && documentOpt.isPresent()) {
            Scheme scheme = schemeOpt.get();
            Document document = documentOpt.get();

            return scheme.getSchemeDocuments().stream()
                    .anyMatch(sd -> sd.getDocument().getId().equals(document.getId()) && sd.isRequired());
        }

        return false;
    }
}
