package com.example.demo.service;

import com.example.demo.model.*;
import com.example.demo.model.GeneralUser;
import com.example.demo.repository.NetCafeCommissionRepository;
import com.example.demo.repository.PaymentRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class PaymentService {

    @Autowired
    private PaymentRecordRepository paymentRecordRepository;

    @Autowired
    private NetCafeCommissionRepository netCafeCommissionRepository;

    /**
     * Record a pending user payment for a scheme application
     * This payment needs to be verified by admin
     */
    @Transactional
    public PaymentRecord recordPendingUserPayment(SchemeApplication application, String transactionId, String paymentMethod, String remarks) {
        PaymentRecord payment = new PaymentRecord();
        payment.setApplication(application);
        payment.setUser(application.getUser());
        payment.setPaymentType("USER_PAYMENT");
        // Convert Double to BigDecimal
        Double paymentAmountDouble = application.getScheme().getPaymentAmount();
        BigDecimal paymentAmount = paymentAmountDouble != null ? BigDecimal.valueOf(paymentAmountDouble) : BigDecimal.ZERO;
        payment.setAmount(paymentAmount);
        payment.setTransactionId(transactionId);
        payment.setPaymentDate(LocalDateTime.now());
        payment.setStatus("PENDING"); // Payment is pending until admin verifies it
        payment.setPaymentMethod(paymentMethod);
        payment.setRemarks(remarks);
        payment.setIsSettled(false); // Not settled until admin verifies

        return paymentRecordRepository.save(payment);
    }

    /**
     * Verify a user payment (by admin)
     */
    @Transactional
    public PaymentRecord verifyUserPayment(Long paymentId, String adminRemarks) {
        PaymentRecord payment = paymentRecordRepository.findById(paymentId)
                .orElseThrow(() -> new RuntimeException("Payment not found with ID: " + paymentId));

        payment.setStatus("COMPLETED");
        payment.setIsSettled(true);
        payment.setSettlementDate(LocalDateTime.now());

        if (adminRemarks != null && !adminRemarks.isEmpty()) {
            payment.setRemarks(payment.getRemarks() + " | Admin: " + adminRemarks);
        }

        // Update the application payment status
        if (payment.getApplication() != null) {
            SchemeApplication application = payment.getApplication();
            application.setPaymentStatus("VERIFIED");
            // We don't need to save the application here as it will be saved by the caller
        }

        return paymentRecordRepository.save(payment);
    }

    /**
     * Record a user payment for a scheme application (already verified)
     */
    @Transactional
    public PaymentRecord recordUserPayment(SchemeApplication application, String transactionId, String paymentMethod, String remarks) {
        PaymentRecord payment = new PaymentRecord();
        payment.setApplication(application);
        payment.setUser(application.getUser());
        payment.setPaymentType("USER_PAYMENT");
        // Convert Double to BigDecimal
        Double paymentAmountDouble = application.getScheme().getPaymentAmount();
        BigDecimal paymentAmount = paymentAmountDouble != null ? BigDecimal.valueOf(paymentAmountDouble) : BigDecimal.ZERO;
        payment.setAmount(paymentAmount);
        payment.setTransactionId(transactionId);
        payment.setPaymentDate(LocalDateTime.now());
        payment.setStatus("COMPLETED");
        payment.setPaymentMethod(paymentMethod);
        payment.setRemarks(remarks);
        payment.setIsSettled(true); // User payments are considered settled immediately
        payment.setSettlementDate(LocalDateTime.now());

        return paymentRecordRepository.save(payment);
    }

    /**
     * Record a NetCafe commission for a processed application
     */
    @Transactional
    public PaymentRecord recordNetCafeCommission(NetCafeApplication netCafeApplication) {
        SchemeApplication application = netCafeApplication.getApplication();
        Scheme scheme = application.getScheme();

        // Find the commission rate for this scheme
        Optional<NetCafeCommission> commissionOpt = netCafeCommissionRepository.findBySchemeAndIsActiveTrue(scheme);

        // If no commission rate is found, use a default fixed amount of 50
        BigDecimal commissionAmount;
        if (commissionOpt.isEmpty()) {
            System.out.println("WARNING: No commission rate found for scheme: " + scheme.getName() + ". Using default commission.");
            commissionAmount = new BigDecimal("50.00"); // Default fixed commission amount
        } else {
            NetCafeCommission commission = commissionOpt.get();

            if (commission.getIsFixedAmount()) {
                commissionAmount = commission.getCommissionAmount();
            } else {
                // Calculate percentage-based commission
                Double schemePaymentAmount = scheme.getPaymentAmount();
                BigDecimal paymentAmount = schemePaymentAmount != null ? BigDecimal.valueOf(schemePaymentAmount) : BigDecimal.ZERO;
                commissionAmount = paymentAmount.multiply(commission.getCommissionPercentage().divide(new BigDecimal("100")));
            }
        }

        PaymentRecord payment = new PaymentRecord();
        payment.setApplication(application);
        payment.setNetCafeApplication(netCafeApplication);
        payment.setNetCafeUser(netCafeApplication.getNetCafeUser());
        payment.setPaymentType("NETCAFE_COMMISSION");
        payment.setAmount(commissionAmount);
        payment.setPaymentDate(LocalDateTime.now());
        payment.setStatus("PENDING");
        payment.setRemarks("Commission for processing application #" + application.getId());
        payment.setIsSettled(false);

        return paymentRecordRepository.save(payment);
    }

    /**
     * Record admin payment to NetCafe user (settlement)
     */
    @Transactional
    public PaymentRecord recordAdminPaymentToNetCafe(NetCafeUser netCafeUser, BigDecimal amount, String transactionId, String paymentMethod, String remarks) {
        PaymentRecord payment = new PaymentRecord();
        payment.setNetCafeUser(netCafeUser);
        payment.setPaymentType("ADMIN_TO_NETCAFE");
        payment.setAmount(amount);
        payment.setTransactionId(transactionId);
        payment.setPaymentDate(LocalDateTime.now());
        payment.setStatus("COMPLETED");
        payment.setPaymentMethod(paymentMethod);
        payment.setRemarks(remarks);
        payment.setIsSettled(true);
        payment.setSettlementDate(LocalDateTime.now());

        return paymentRecordRepository.save(payment);
    }

    /**
     * Settle pending NetCafe commissions
     */
    @Transactional
    public void settleNetCafeCommissions(NetCafeUser netCafeUser, String transactionId, String paymentMethod) {
        List<PaymentRecord> unsettledPayments = paymentRecordRepository.findByNetCafeUserAndIsSettledFalseOrderByPaymentDateDesc(netCafeUser);

        if (unsettledPayments.isEmpty()) {
            return;
        }

        BigDecimal totalAmount = BigDecimal.ZERO;

        for (PaymentRecord payment : unsettledPayments) {
            if ("NETCAFE_COMMISSION".equals(payment.getPaymentType())) {
                totalAmount = totalAmount.add(payment.getAmount());
                payment.setIsSettled(true);
                payment.setSettlementDate(LocalDateTime.now());
                payment.setStatus("COMPLETED");
                paymentRecordRepository.save(payment);
            }
        }

        if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
            // Record the settlement payment
            recordAdminPaymentToNetCafe(netCafeUser, totalAmount, transactionId, paymentMethod,
                    "Settlement for " + unsettledPayments.size() + " commission payments");
        }
    }

    /**
     * Get payment history for a user
     */
    public List<PaymentRecord> getUserPaymentHistory(GeneralUser user) {
        return paymentRecordRepository.findByUserOrderByPaymentDateDesc(user);
    }

    /**
     * Get payment history for a NetCafe user
     */
    public List<PaymentRecord> getNetCafePaymentHistory(NetCafeUser netCafeUser) {
        return paymentRecordRepository.findByNetCafeUserOrderByPaymentDateDesc(netCafeUser);
    }

    /**
     * Get recent payments for a user
     */
    public List<PaymentRecord> getRecentUserPayments(GeneralUser user) {
        return paymentRecordRepository.findRecentPaymentsByUser(user);
    }

    /**
     * Get recent payments for a NetCafe user
     */
    public List<PaymentRecord> getRecentNetCafePayments(NetCafeUser netCafeUser) {
        return paymentRecordRepository.findRecentPaymentsByNetCafeUser(netCafeUser);
    }

    /**
     * Get total unsettled amount for a NetCafe user
     */
    public BigDecimal getTotalUnsettledAmountForNetCafe(NetCafeUser netCafeUser) {
        Double amount = paymentRecordRepository.findTotalUnsettledAmountForNetCafeUser(netCafeUser);
        return amount != null ? BigDecimal.valueOf(amount) : BigDecimal.ZERO;
    }

    /**
     * Get payments by date range
     */
    public List<PaymentRecord> getPaymentsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return paymentRecordRepository.findByPaymentDateBetweenOrderByPaymentDateDesc(startDate, endDate);
    }

    /**
     * Get payments for a specific application
     */
    public List<PaymentRecord> getPaymentsForApplication(SchemeApplication application) {
        return paymentRecordRepository.findByApplicationOrderByPaymentDateDesc(application);
    }

    /**
     * Get all pending user payments that need admin verification
     */
    public List<PaymentRecord> getAllPendingUserPayments() {
        return paymentRecordRepository.findByPaymentTypeAndStatusOrderByPaymentDateDesc("USER_PAYMENT", "PENDING");
    }

    /**
     * Get payment by ID
     */
    public Optional<PaymentRecord> getPaymentById(Long id) {
        return paymentRecordRepository.findById(id);
    }

    /**
     * Get all payments
     */
    public List<PaymentRecord> getAllPayments() {
        return paymentRecordRepository.findAll();
    }

    /**
     * Save a payment record
     */
    @Transactional
    public PaymentRecord savePayment(PaymentRecord payment) {
        return paymentRecordRepository.save(payment);
    }
}
