<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .header-card {
            background-color: #dc3545;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .btn-danger {
            background-color: #dc3545;
            border: none;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
            padding: 5px 10px;
            border-radius: 5px;
        }
        .badge-approved {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
        }
        .badge-rejected {
            background-color: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
        }
        .section-title {
            color: #495057;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/schemes/applications}">
                            <i class="bi bi-file-earmark-text"></i> Scheme Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes}">
                            <i class="bi bi-list-check"></i> Manage Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/documents}">
                            <i class="bi bi-file-earmark"></i> Document Types
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2>Application Details</h2>
                    <p>Review and manage scheme application</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span>Application Information</span>
                                <span th:if="${application.status == 'PENDING'}" class="badge badge-pending">Pending</span>
                                <span th:if="${application.status == 'APPROVED'}" class="badge badge-approved">Approved</span>
                                <span th:if="${application.status == 'REJECTED'}" class="badge badge-rejected">Rejected</span>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5 class="section-title">Application Details</h5>
                                        <p><strong>Application ID:</strong> <span th:text="${application.id}">1</span></p>
                                        <p><strong>Application Date:</strong> <span th:text="${#temporals.format(application.applicationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                        <p><strong>Status:</strong> <span th:text="${application.status}">PENDING</span></p>
                                        <p th:if="${application.remarks != null && !application.remarks.isEmpty()}">
                                            <strong>Remarks:</strong> <span th:text="${application.remarks}">Remarks</span>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5 class="section-title">Scheme Information</h5>
                                        <div th:if="${application.scheme != null}">
                                            <p><strong>Scheme Name:</strong> <span th:text="${application.scheme.name}">Scheme Name</span></p>
                                            <p><strong>Scheme ID:</strong> <span th:text="${application.scheme.id}">1</span></p>
                                            <a th:href="@{/admin/schemes/edit/{id}(id=${application.scheme.id})}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i> View Scheme Details
                                            </a>
                                        </div>
                                        <div th:if="${application.scheme == null}" class="alert alert-warning">
                                            <i class="bi bi-exclamation-triangle"></i> Scheme information not available
                                        </div>
                                    </div>
                                </div>

                                <hr>

                                <div class="row mt-4">
                                    <div class="col-md-6">
                                        <h5 class="section-title">Applicant Information</h5>
                                        <p><strong>Name:</strong> <span th:text="${application.user.name}">John Doe</span></p>
                                        <p><strong>Email:</strong> <span th:text="${application.user.email}"><EMAIL></span></p>
                                        <p><strong>Mobile:</strong> <span th:text="${application.user.mobileNumber}">1234567890</span></p>
                                        <p><strong>Address:</strong> <span th:text="${application.user.address}">123 Main St</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5 class="section-title">Required Documents</h5>
                                        <div th:if="${requiredDocuments != null && !requiredDocuments.empty}" class="mb-3">
                                            <ul class="list-group">
                                                <li class="list-group-item" th:each="doc : ${requiredDocuments}">
                                                    <i class="bi bi-file-earmark-text text-success me-2"></i>
                                                    <strong th:text="${doc.name}">Document Name</strong>
                                                </li>
                                            </ul>
                                        </div>
                                        <div th:if="${requiredDocuments == null || requiredDocuments.empty}" class="alert alert-info">
                                            <i class="bi bi-info-circle"></i> No specific documents were required for this scheme.
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <h5 class="section-title">Submitted Documents</h5>

                                        <!-- Individual document uploads -->
                                        <div th:if="${application.applicationDocuments != null && !application.applicationDocuments.empty}" class="mb-3">
                                            <div class="table-responsive">
                                                <table class="table table-bordered">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th>Document Type</th>
                                                            <th>File Name</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr th:each="appDoc : ${application.applicationDocuments}">
                                                            <td th:text="${appDoc.document != null ? appDoc.document.name : 'Unknown'}">Document Name</td>
                                                            <td th:text="${appDoc.documentName != null ? appDoc.documentName : 'Unknown'}">file.pdf</td>
                                                            <td>
                                                                <a th:if="${appDoc.document != null}"
                                                                   th:href="@{/admin/schemes/applications/document/{appId}/{docId}(appId=${application.id},docId=${appDoc.document.id})}"
                                                                   class="btn btn-sm btn-outline-primary" target="_blank">
                                                                    <i class="bi bi-eye"></i> View
                                                                </a>
                                                                <span th:if="${appDoc.document == null}" class="text-muted">Not available</span>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        <!-- Consolidated document (if any) -->
                                        <div th:if="${application.supportingDocument != null}" class="mt-3">
                                            <h6>Additional Supporting Document</h6>
                                            <a th:href="@{/admin/schemes/applications/document/{id}(id=${application.id})}" class="btn btn-outline-primary" target="_blank">
                                                <i class="bi bi-file-earmark-pdf"></i> View Consolidated Document
                                            </a>
                                        </div>

                                        <!-- No documents message -->
                                        <div th:if="${(application.applicationDocuments == null || application.applicationDocuments.empty) && application.supportingDocument == null}" class="alert alert-warning">
                                            <i class="bi bi-exclamation-triangle"></i> No documents were submitted with this application.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" th:if="${application.status == 'PENDING'}">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                Update Application Status
                            </div>
                            <div class="card-body">
                                <form th:action="@{/admin/schemes/applications/{id}/update-status(id=${application.id})}" method="post">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status</label>
                                        <select class="form-select" id="status" name="status" required>
                                            <option value="PENDING" th:selected="${application.status == 'PENDING'}">Pending</option>
                                            <option value="APPROVED" th:selected="${application.status == 'APPROVED'}">Approved</option>
                                            <option value="REJECTED" th:selected="${application.status == 'REJECTED'}">Rejected</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="remarks" class="form-label">Remarks</label>
                                        <textarea class="form-control" id="remarks" name="remarks" rows="3" th:text="${application.remarks}"></textarea>
                                    </div>
                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-danger">Update Status</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between">
                            <a th:href="@{/admin/schemes/applications}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Applications
                            </a>
                            <a th:href="@{/admin/messages/{id}(id=${application.id})}" class="btn btn-primary">
                                <i class="bi bi-chat-dots"></i> View Messages
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
