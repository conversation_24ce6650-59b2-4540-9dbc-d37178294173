package com.example.demo.service;

import com.example.demo.model.NetCafeUser;
import com.example.demo.repository.NetCafeUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class NetCafeUserService {

    @Autowired
    private NetCafeUserRepository netCafeUserRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    public NetCafeUserService() {
        // Constructor
    }

    public NetCafeUser registerUser(NetCafeUser user, MultipartFile photo, MultipartFile cscCertificate,
                                MultipartFile aadharCardPhoto, MultipartFile panCardPhoto) throws IOException {
        // Encode password
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // Set registration date
        user.setRegistrationDate(LocalDateTime.now());

        // Set approved to false by default
        user.setApproved(false);

        // Save photo
        user.setPhoto(photo.getBytes());
        user.setPhotoContentType(photo.getContentType());

        // Save CSC certificate
        user.setCscCertificate(cscCertificate.getBytes());
        user.setCscCertificateContentType(cscCertificate.getContentType());

        // Save Aadhar card photo
        user.setAadharCardPhoto(aadharCardPhoto.getBytes());
        user.setAadharCardPhotoContentType(aadharCardPhoto.getContentType());

        // Save PAN card photo
        user.setPanCardPhoto(panCardPhoto.getBytes());
        user.setPanCardPhotoContentType(panCardPhoto.getContentType());

        // Save user
        return netCafeUserRepository.save(user);
    }

    public Optional<NetCafeUser> findByEmail(String email) {
        return netCafeUserRepository.findByEmail(email);
    }

    public boolean authenticateUser(String email, String password) {
        Optional<NetCafeUser> userOpt = netCafeUserRepository.findByEmail(email);

        if (userOpt.isPresent()) {
            NetCafeUser user = userOpt.get();
            return passwordEncoder.matches(password, user.getPassword()) && user.isApproved();
        }

        return false;
    }

    public List<NetCafeUser> findAllPendingApprovals() {
        return netCafeUserRepository.findByApproved(false);
    }

    public List<NetCafeUser> findAllApprovedUsers() {
        return netCafeUserRepository.findByApproved(true);
    }

    public List<NetCafeUser> findAllUsers() {
        return netCafeUserRepository.findAll();
    }

    public NetCafeUser approveUser(Long userId) {
        Optional<NetCafeUser> userOpt = netCafeUserRepository.findById(userId);

        if (userOpt.isPresent()) {
            NetCafeUser user = userOpt.get();
            user.setApproved(true);
            return netCafeUserRepository.save(user);
        }

        throw new RuntimeException("User not found with ID: " + userId);
    }

    public boolean isEmailTaken(String email) {
        return netCafeUserRepository.existsByEmail(email);
    }

    public boolean isAadharTaken(String aadharNumber) {
        return netCafeUserRepository.existsByAadharNumber(aadharNumber);
    }

    public boolean isPanTaken(String panNumber) {
        return netCafeUserRepository.existsByPanNumber(panNumber);
    }

    public Optional<NetCafeUser> findById(Long id) {
        return netCafeUserRepository.findById(id);
    }

    public NetCafeUser updateUserProfile(
            Long userId,
            NetCafeUser updatedUser,
            String newPassword,
            MultipartFile photo,
            MultipartFile cscCertificate,
            MultipartFile aadharCardPhoto,
            MultipartFile panCardPhoto) throws IOException {

        Optional<NetCafeUser> userOpt = netCafeUserRepository.findById(userId);

        if (userOpt.isEmpty()) {
            throw new RuntimeException("User not found with ID: " + userId);
        }

        NetCafeUser existingUser = userOpt.get();

        // Update basic information
        existingUser.setName(updatedUser.getName());
        existingUser.setMobileNumber(updatedUser.getMobileNumber());

        // Update password if provided
        if (newPassword != null && !newPassword.trim().isEmpty()) {
            existingUser.setPassword(passwordEncoder.encode(newPassword));
        }

        // Update photo if provided
        if (photo != null && !photo.isEmpty()) {
            existingUser.setPhoto(photo.getBytes());
            existingUser.setPhotoContentType(photo.getContentType());
        }

        // Update CSC certificate if provided
        if (cscCertificate != null && !cscCertificate.isEmpty()) {
            existingUser.setCscCertificate(cscCertificate.getBytes());
            existingUser.setCscCertificateContentType(cscCertificate.getContentType());
        }

        // Update Aadhar card photo if provided
        if (aadharCardPhoto != null && !aadharCardPhoto.isEmpty()) {
            existingUser.setAadharCardPhoto(aadharCardPhoto.getBytes());
            existingUser.setAadharCardPhotoContentType(aadharCardPhoto.getContentType());
        }

        // Update PAN card photo if provided
        if (panCardPhoto != null && !panCardPhoto.isEmpty()) {
            existingUser.setPanCardPhoto(panCardPhoto.getBytes());
            existingUser.setPanCardPhotoContentType(panCardPhoto.getContentType());
        }

        // Save updated user
        return netCafeUserRepository.save(existingUser);
    }

    public List<NetCafeUser> getAllActiveNetCafeUsers() {
        return netCafeUserRepository.findByActiveTrue();
    }

    public Optional<NetCafeUser> getNetCafeUserById(Long id) {
        return netCafeUserRepository.findById(id);
    }

    public NetCafeUser toggleStatus(Long userId) {
        Optional<NetCafeUser> userOpt = netCafeUserRepository.findById(userId);

        if (userOpt.isPresent()) {
            NetCafeUser user = userOpt.get();
            user.setActive(!user.isActive()); // Toggle active status
            return netCafeUserRepository.save(user);
        } else {
            throw new RuntimeException("NetCafe user not found with ID: " + userId);
        }
    }
}
