<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Manage NetCafe Users</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            min-height: 100vh;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #495057;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 0;
            padding: 20px;
        }
        .welcome-card {
            background-color: #007bff;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .card {
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
        }
        @media (min-width: 768px) {
            .content {
                margin-left: 16.666667%;
            }
        }
        @media (max-width: 767.98px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: auto;
                z-index: 1000;
                padding-top: 0;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    
                    <!-- User Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/users}">
                            <i class="bi bi-person"></i> General Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/netcafe-users}">
                            <i class="bi bi-people"></i> NetCafe Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/netcafe-approvals}">
                            <i class="bi bi-check2-circle"></i> NetCafe Approvals
                        </a>
                    </li>
                    
                    <!-- Scheme Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes}">
                            <i class="bi bi-list-check"></i> Manage Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes/applications}">
                            <i class="bi bi-file-earmark-text"></i> Scheme Applications
                        </a>
                    </li>
                    
                    <!-- Payment Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/pending-payments}">
                            <i class="bi bi-credit-card"></i> Verify Payments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/commissions}">
                            <i class="bi bi-cash-coin"></i> NetCafe Commissions
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="welcome-card">
                    <h2>Manage NetCafe Users</h2>
                    <p>View and manage all registered NetCafe users on the platform.</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>All NetCafe Users</span>
                        <span class="badge bg-primary" th:text="${users.size()} + ' users'">0 users</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Mobile</th>
                                        <th>Aadhar Number</th>
                                        <th>PAN Number</th>
                                        <th>Registration Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:if="${users.empty}">
                                        <td colspan="9" class="text-center">No NetCafe users found</td>
                                    </tr>
                                    <tr th:each="user : ${users}">
                                        <td th:text="${user.id}">1</td>
                                        <td th:text="${user.name}">John Doe</td>
                                        <td th:text="${user.email}"><EMAIL></td>
                                        <td th:text="${user.mobileNumber}">1234567890</td>
                                        <td th:text="${user.aadharNumber}">123456789012</td>
                                        <td th:text="${user.panNumber}">**********</td>
                                        <td th:text="${#temporals.format(user.registrationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</td>
                                        <td>
                                            <span th:if="${user.approved && user.active}" class="badge bg-success">Active</span>
                                            <span th:if="${user.approved && !user.active}" class="badge bg-danger">Inactive</span>
                                            <span th:unless="${user.approved}" class="badge bg-warning">Pending</span>
                                        </td>
                                        <td>
                                            <div class="d-flex">
                                                <a th:href="@{/admin/netcafe-users/{id}(id=${user.id})}" class="btn btn-primary btn-sm me-2">
                                                    <i class="bi bi-eye"></i> View
                                                </a>
                                                <form th:if="${user.approved}" th:action="@{/admin/netcafe-users/{id}/toggle-status(id=${user.id})}" method="post" class="me-2">
                                                    <button type="submit" class="btn btn-warning btn-sm">
                                                        <i class="bi bi-toggle-on"></i> 
                                                        <span th:if="${user.active}">Deactivate</span>
                                                        <span th:unless="${user.active}">Activate</span>
                                                    </button>
                                                </form>
                                                <a th:unless="${user.approved}" th:href="@{/admin/view-documents/{id}(id=${user.id})}" class="btn btn-info btn-sm me-2">
                                                    <i class="bi bi-file-earmark"></i> Review
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
