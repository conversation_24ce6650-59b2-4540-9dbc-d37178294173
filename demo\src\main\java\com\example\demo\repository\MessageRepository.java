package com.example.demo.repository;

import com.example.demo.model.Message;
import com.example.demo.model.NetCafeApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {

    // Find all messages for a specific NetCafeApplication
    List<Message> findByNetCafeApplicationOrderBySentDateAsc(NetCafeApplication netCafeApplication);

    // Find all unread messages for a specific NetCafeApplication
    List<Message> findByNetCafeApplicationAndIsReadFalseOrderBySentDateAsc(NetCafeApplication netCafeApplication);

    // Find all unread messages for a specific NetCafeApplication and sender type
    List<Message> findByNetCafeApplicationAndSenderTypeAndIsReadFalseOrderBySentDateAsc(
            NetCafeApplication netCafeApplication, String senderType);

    // Count unread messages for a specific NetCafeApplication
    long countByNetCafeApplicationAndIsReadFalse(NetCafeApplication netCafeApplication);

    // Count unread messages for a specific NetCafeApplication and sender type
    long countByNetCafeApplicationAndSenderTypeAndIsReadFalse(NetCafeApplication netCafeApplication, String senderType);

    // Find all messages with attachments for a specific NetCafeApplication
    List<Message> findByNetCafeApplicationAndAttachmentIsNotNullOrderBySentDateDesc(NetCafeApplication netCafeApplication);

    // Find all messages with attachments of a specific type for a NetCafeApplication
    List<Message> findByNetCafeApplicationAndAttachmentTypeOrderBySentDateDesc(
            NetCafeApplication netCafeApplication, String attachmentType);
}
