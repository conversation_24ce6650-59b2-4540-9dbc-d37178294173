package com.example.demo.repository;

import com.example.demo.model.ApplicationHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ApplicationHistoryRepository extends JpaRepository<ApplicationHistory, Long> {

    // Find by scheme application ID, ordered by creation date (most recent first)
    @Query("SELECT ah FROM ApplicationHistory ah WHERE ah.schemeApplicationId = :schemeApplicationId ORDER BY ah.createdAt DESC")
    List<ApplicationHistory> findBySchemeApplicationIdOrderByCreatedAtDesc(Long schemeApplicationId);

    // Find by NetCafe application ID, ordered by creation date (most recent first)
    @Query("SELECT ah FROM ApplicationHistory ah WHERE ah.netCafeApplicationId = :netCafeApplicationId ORDER BY ah.createdAt DESC")
    List<ApplicationHistory> findByNetCafeApplicationIdOrderByCreatedAtDesc(Long netCafeApplicationId);

    // Find by user ID
    List<ApplicationHistory> findByUserIdOrderByCreatedAtDesc(Long userId);

    // Find by NetCafe user ID
    List<ApplicationHistory> findByNetCafeUserIdOrderByCreatedAtDesc(Long netCafeUserId);

    // Find by scheme ID
    List<ApplicationHistory> findBySchemeIdOrderByCreatedAtDesc(Long schemeId);

    // Find by completion receipt ID
    Optional<ApplicationHistory> findByCompletionReceiptId(String completionReceiptId);

    // Find by final status
    List<ApplicationHistory> findByFinalStatusOrderByCreatedAtDesc(String finalStatus);

    // Find confirmed applications
    @Query("SELECT ah FROM ApplicationHistory ah WHERE ah.confirmationDate IS NOT NULL ORDER BY ah.confirmationDate DESC")
    List<ApplicationHistory> findConfirmedApplications();

    // Find unconfirmed applications
    @Query("SELECT ah FROM ApplicationHistory ah WHERE ah.completionDate IS NOT NULL AND ah.confirmationDate IS NULL ORDER BY ah.completionDate DESC")
    List<ApplicationHistory> findUnconfirmedApplications();

    // Find recent history for a user
    @Query("SELECT ah FROM ApplicationHistory ah WHERE ah.userId = ?1 ORDER BY ah.createdAt DESC LIMIT 5")
    List<ApplicationHistory> findRecentHistoryForUser(Long userId);

    // Find recent history for a NetCafe user
    @Query("SELECT ah FROM ApplicationHistory ah WHERE ah.netCafeUserId = ?1 ORDER BY ah.createdAt DESC LIMIT 5")
    List<ApplicationHistory> findRecentHistoryForNetCafeUser(Long netCafeUserId);
}
