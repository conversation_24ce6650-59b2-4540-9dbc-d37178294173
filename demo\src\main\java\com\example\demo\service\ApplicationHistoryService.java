package com.example.demo.service;

import com.example.demo.model.ApplicationHistory;
import com.example.demo.model.NetCafeApplication;
import com.example.demo.model.SchemeApplication;
import com.example.demo.repository.ApplicationHistoryRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
public class ApplicationHistoryService {

    @Autowired
    private ApplicationHistoryRepository applicationHistoryRepository;

    @Autowired
    private SchemeApplicationService schemeApplicationService;

    @Autowired
    private NetCafeApplicationService netCafeApplicationService;

    @Autowired
    private PaymentService paymentService;

    /**
     * Create a new application history entry when NetCafe marks an application as completed
     */
    @Transactional
    public ApplicationHistory createApplicationHistory(SchemeApplication schemeApplication, NetCafeApplication netCafeApplication) {
        // Generate a unique receipt ID
        String receiptId = generateReceiptId();

        // Create a new history entry
        ApplicationHistory history = new ApplicationHistory(schemeApplication, netCafeApplication, receiptId);

        // Save the history entry
        ApplicationHistory savedHistory = applicationHistoryRepository.save(history);

        // Note: NetCafe commission will be recorded when the bond is broken
        // This ensures NetCafe gets paid only after the entire process is complete

        return savedHistory;
    }

    /**
     * Confirm application completion by the user
     */
    @Transactional
    public ApplicationHistory confirmApplicationCompletion(Long historyId) {
        Optional<ApplicationHistory> historyOpt = applicationHistoryRepository.findById(historyId);

        if (historyOpt.isEmpty()) {
            throw new RuntimeException("Application history not found with ID: " + historyId);
        }

        ApplicationHistory history = historyOpt.get();
        history.setConfirmationDate(LocalDateTime.now());

        return applicationHistoryRepository.save(history);
    }

    /**
     * Confirm application completion by receipt ID
     */
    @Transactional
    public ApplicationHistory confirmApplicationCompletionByReceiptId(String receiptId) {
        Optional<ApplicationHistory> historyOpt = applicationHistoryRepository.findByCompletionReceiptId(receiptId);

        if (historyOpt.isEmpty()) {
            throw new RuntimeException("Application history not found with receipt ID: " + receiptId);
        }

        ApplicationHistory history = historyOpt.get();
        history.setConfirmationDate(LocalDateTime.now());

        // Update the scheme application and netcafe application status
        if (history.getSchemeApplicationId() != null) {
            Optional<SchemeApplication> schemeAppOpt = schemeApplicationService.getApplicationById(history.getSchemeApplicationId());
            if (schemeAppOpt.isPresent()) {
                SchemeApplication schemeApp = schemeAppOpt.get();
                schemeApp.setStatus("CONFIRMED");
                schemeApplicationService.saveApplication(schemeApp);
            }
        }

        if (history.getNetCafeApplicationId() != null) {
            Optional<NetCafeApplication> netCafeAppOpt = netCafeApplicationService.getApplicationById(history.getNetCafeApplicationId());
            if (netCafeAppOpt.isPresent()) {
                NetCafeApplication netCafeApp = netCafeAppOpt.get();
                netCafeApp.setStatus("CONFIRMED");
                netCafeApp.setConfirmationDate(LocalDateTime.now());
                netCafeApplicationService.saveApplication(netCafeApp);

                // Note: NetCafe commission will be recorded when the bond is broken
                // This ensures NetCafe gets paid only after the entire process is complete
            }
        }

        return applicationHistoryRepository.save(history);
    }

    /**
     * Break the bond between user and NetCafe
     */
    @Transactional
    public ApplicationHistory breakBond(Long historyId, String reason, String initiator) {
        Optional<ApplicationHistory> historyOpt = applicationHistoryRepository.findById(historyId);

        if (historyOpt.isEmpty()) {
            throw new RuntimeException("Application history not found with ID: " + historyId);
        }

        ApplicationHistory history = historyOpt.get();

        // Update the scheme application status
        if (history.getSchemeApplicationId() != null) {
            Optional<SchemeApplication> schemeAppOpt = schemeApplicationService.getApplicationById(history.getSchemeApplicationId());
            if (schemeAppOpt.isPresent()) {
                SchemeApplication schemeApp = schemeAppOpt.get();
                schemeApp.setStatus("BOND_BROKEN");
                schemeApp.setBondBroken(true);
                schemeApp.setBondBrokenDate(LocalDateTime.now());
                schemeApp.setBondBreakReason(reason);
                schemeApplicationService.saveApplication(schemeApp);
            }
        }

        // Update the netcafe application status
        if (history.getNetCafeApplicationId() != null) {
            Optional<NetCafeApplication> netCafeAppOpt = netCafeApplicationService.getApplicationById(history.getNetCafeApplicationId());
            if (netCafeAppOpt.isPresent()) {
                NetCafeApplication netCafeApp = netCafeAppOpt.get();
                netCafeApp.setStatus("BOND_BROKEN");
                netCafeApp.setBondBroken(true);
                netCafeApp.setBondBrokenDate(LocalDateTime.now());
                netCafeApp.setBondBreakReason(reason);
                netCafeApp.setBondBreakInitiator(initiator);
                netCafeApplicationService.saveApplication(netCafeApp);

                // Record NetCafe commission when bond is broken
                // This is the final step in the process, and NetCafe should receive their payment now
                try {
                    paymentService.recordNetCafeCommission(netCafeApp);
                    System.out.println("NetCafe commission recorded for application: " + netCafeApp.getId());
                } catch (Exception e) {
                    System.out.println("Error recording NetCafe commission: " + e.getMessage());
                    // Throw exception to ensure transaction is rolled back if commission recording fails
                    throw new RuntimeException("Failed to record NetCafe commission: " + e.getMessage(), e);
                }
            }
        }

        // Update the history
        history.setFinalStatus("BOND_BROKEN");
        history.setBondBroken(true);
        history.setBondBrokenDate(LocalDateTime.now());
        history.setBondBreakReason(reason);
        history.setBondBreakInitiator(initiator);

        return applicationHistoryRepository.save(history);
    }

    /**
     * Break the bond by receipt ID
     */
    @Transactional
    public ApplicationHistory breakBondByReceiptId(String receiptId, String reason, String initiator) {
        Optional<ApplicationHistory> historyOpt = applicationHistoryRepository.findByCompletionReceiptId(receiptId);

        if (historyOpt.isEmpty()) {
            throw new RuntimeException("Application history not found with receipt ID: " + receiptId);
        }

        return breakBond(historyOpt.get().getId(), reason, initiator);
    }

    /**
     * Get application history by ID
     */
    public Optional<ApplicationHistory> getApplicationHistoryById(Long id) {
        return applicationHistoryRepository.findById(id);
    }

    /**
     * Get application history by scheme application ID
     * Returns the most recent history record for a scheme application
     */
    public Optional<ApplicationHistory> getApplicationHistoryBySchemeApplicationId(Long schemeApplicationId) {
        List<ApplicationHistory> historyList = applicationHistoryRepository.findBySchemeApplicationIdOrderByCreatedAtDesc(schemeApplicationId);
        if (historyList.isEmpty()) {
            return Optional.empty();
        }
        // Return the most recent history record (first in the list due to DESC ordering)
        return Optional.of(historyList.get(0));
    }

    /**
     * Get application history by NetCafe application ID
     * Returns the most recent history record for a NetCafe application
     */
    public Optional<ApplicationHistory> getApplicationHistoryByNetCafeApplicationId(Long netCafeApplicationId) {
        List<ApplicationHistory> historyList = applicationHistoryRepository.findByNetCafeApplicationIdOrderByCreatedAtDesc(netCafeApplicationId);
        if (historyList.isEmpty()) {
            return Optional.empty();
        }
        // Return the most recent history record (first in the list due to DESC ordering)
        return Optional.of(historyList.get(0));
    }

    /**
     * Get application history by receipt ID
     */
    public Optional<ApplicationHistory> getApplicationHistoryByReceiptId(String receiptId) {
        return applicationHistoryRepository.findByCompletionReceiptId(receiptId);
    }



    /**
     * Get application history for a user
     */
    public List<ApplicationHistory> getApplicationHistoryForUser(Long userId) {
        return applicationHistoryRepository.findByUserIdOrderByCreatedAtDesc(userId);
    }

    /**
     * Get application history for a NetCafe user
     */
    public List<ApplicationHistory> getApplicationHistoryForNetCafeUser(Long netCafeUserId) {
        return applicationHistoryRepository.findByNetCafeUserIdOrderByCreatedAtDesc(netCafeUserId);
    }

    /**
     * Get recent application history for a user
     */
    public List<ApplicationHistory> getRecentApplicationHistoryForUser(Long userId) {
        return applicationHistoryRepository.findRecentHistoryForUser(userId);
    }

    /**
     * Get recent application history for a NetCafe user
     */
    public List<ApplicationHistory> getRecentApplicationHistoryForNetCafeUser(Long netCafeUserId) {
        return applicationHistoryRepository.findRecentHistoryForNetCafeUser(netCafeUserId);
    }

    /**
     * Get all confirmed applications
     */
    public List<ApplicationHistory> getAllConfirmedApplications() {
        return applicationHistoryRepository.findConfirmedApplications();
    }

    /**
     * Get all unconfirmed applications
     */
    public List<ApplicationHistory> getAllUnconfirmedApplications() {
        return applicationHistoryRepository.findUnconfirmedApplications();
    }

    /**
     * Get all applications with broken bonds for a user
     */
    public List<ApplicationHistory> getBrokenBondApplicationsForUser(Long userId) {
        List<ApplicationHistory> history = applicationHistoryRepository.findByUserIdOrderByCreatedAtDesc(userId);

        return history.stream()
                .filter(h -> h.getBondBroken() != null && h.getBondBroken())
                .toList();
    }

    /**
     * Get all applications with broken bonds for a NetCafe user
     */
    public List<ApplicationHistory> getBrokenBondApplicationsForNetCafeUser(Long netCafeUserId) {
        List<ApplicationHistory> history = applicationHistoryRepository.findByNetCafeUserIdOrderByCreatedAtDesc(netCafeUserId);

        return history.stream()
                .filter(h -> h.getBondBroken() != null && h.getBondBroken())
                .toList();
    }

    /**
     * Generate a unique receipt ID
     */
    private String generateReceiptId() {
        return "RCPT-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
