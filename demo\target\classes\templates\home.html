<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NetCafe Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .hero-section {
            background-color: #007bff;
            color: white;
            padding: 60px 0;
            margin-bottom: 40px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s;
            margin-bottom: 20px;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            border-radius: 10px 10px 0 0 !important;
        }
        .btn-primary {
            background-color: #007bff;
            border: none;
        }
        .btn-primary:hover {
            background-color: #0069d9;
        }
        .footer {
            background-color: #343a40;
            color: white;
            padding: 20px 0;
            margin-top: 40px;
        }
    </style>
</head>
<body>
    <div class="hero-section text-center">
        <div class="container">
            <h1 class="display-4">NetCafe Management System</h1>
            <p class="lead">A comprehensive solution for NetCafe registration and administration</p>
        </div>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header text-center py-3">
                        <h3>NetCafe</h3>
                    </div>
                    <div class="card-body text-center">
                        <p>Register your NetCafe or login to your existing account</p>
                        <div class="d-grid gap-2">
                            <a href="/netcafe/register" class="btn btn-primary btn-lg mb-2">Register</a>
                            <a href="/netcafe/login" class="btn btn-outline-primary btn-lg">Login</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header text-center py-3" style="background-color: #28a745;">
                        <h3>User</h3>
                    </div>
                    <div class="card-body text-center">
                        <p>Login to access government schemes and apply online</p>
                        <div class="d-grid gap-2">
                            <a href="/user/register" class="btn btn-success btn-lg mb-2">Register</a>
                            <a href="/user/login" class="btn btn-outline-success btn-lg">Login</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header text-center py-3" style="background-color: #dc3545;">
                        <h3>Admin</h3>
                    </div>
                    <div class="card-body text-center">
                        <p>Login to the admin panel to manage system</p>
                        <div class="d-grid gap-2">
                            <a href="/admin/login" class="btn btn-danger btn-lg">Admin Login</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="footer text-center">
        <div class="container">
            <p>&copy; 2023 NetCafe Management System. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
