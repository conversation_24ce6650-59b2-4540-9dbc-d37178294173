<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pending Payments - Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #0d6efd;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/pending-payments}">
                            <i class="bi bi-credit-card"></i> Pending Payments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 content">
                <div class="header-card">
                    <h2>Pending Payments</h2>
                    <p>Review and verify user payments for scheme applications</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>Pending Payment Verification</h5>
                        <a th:href="@{/admin/create-test-payment}" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus-circle"></i> Create Test Payment
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Payment ID</th>
                                        <th>User</th>
                                        <th>Scheme</th>
                                        <th>Amount</th>
                                        <th>Transaction ID</th>
                                        <th>Payment Method</th>
                                        <th>Date</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:if="${pendingPayments.empty}">
                                        <td colspan="9" class="text-center">No pending payments found</td>
                                    </tr>
                                    <tr th:each="payment : ${pendingPayments}">
                                        <td th:text="${payment.id}">1</td>
                                        <td th:text="${payment.user != null ? payment.user.name : 'Unknown'}">User Name</td>
                                        <td th:text="${payment.application != null && payment.application.scheme != null ? payment.application.scheme.name : 'Unknown'}">Scheme Name</td>
                                        <td>₹<span th:text="${payment.amount}">500.00</span></td>
                                        <td th:text="${payment.transactionId}">TXN123456</td>
                                        <td th:text="${payment.paymentMethod}">UPI</td>
                                        <td th:text="${#temporals.format(payment.paymentDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</td>
                                        <td>
                                            <span class="badge bg-warning">Pending</span>
                                        </td>
                                        <td>
                                            <a th:href="@{/admin/payment/{id}(id=${payment.id})}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
