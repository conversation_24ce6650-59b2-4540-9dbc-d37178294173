<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Break Bond</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #0d6efd;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-completed {
            background-color: #28a745;
            color: white;
        }
        .badge-confirmed {
            background-color: #0d6efd;
            color: white;
        }
        .badge-rejected {
            background-color: #dc3545;
            color: white;
        }
        .receipt-id {
            font-family: monospace;
            font-size: 0.9rem;
            font-weight: bold;
            color: #0d6efd;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>NetCafe Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/available-applications}">
                            <i class="bi bi-list-check"></i> Available Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/payments}">
                            <i class="bi bi-credit-card"></i> Payment History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/netcafe/application-history}">
                            <i class="bi bi-clock-history"></i> Application History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Break Bond</h2>
                    <div>
                        <a th:href="@{/netcafe/application-history}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to History
                        </a>
                    </div>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>
                <div class="alert alert-info" role="alert" th:if="${info}" th:text="${info}"></div>

                <div class="card mb-4">
                    <div class="card-header bg-danger text-white">
                        <i class="bi bi-exclamation-triangle"></i> Break Bond Confirmation
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <p><strong>Warning:</strong> Breaking the bond with a user is a permanent action and cannot be undone. This will end your relationship with the user for this application.</p>
                        </div>

                        <div class="mb-4">
                            <h5>Application Details</h5>
                            <p><strong>Scheme:</strong> <span th:text="${history.schemeName}">Scheme Name</span></p>
                            <p><strong>User:</strong> <span th:text="${history.userName}">User Name</span></p>
                            <p><strong>Receipt ID:</strong> <span class="receipt-id" th:text="${history.completionReceiptId}">RCPT-12345678</span></p>
                            <p><strong>Application Date:</strong> <span th:text="${#temporals.format(history.applicationDate, 'dd-MM-yyyy')}">01-01-2023</span></p>
                            <p><strong>Completion Date:</strong> <span th:text="${history.completionDate != null ? #temporals.format(history.completionDate, 'dd-MM-yyyy') : 'N/A'}">02-01-2023</span></p>
                        </div>

                        <form th:action="@{/netcafe/application/break-bond}" method="post">
                            <input type="hidden" name="receiptId" th:value="${history.completionReceiptId}">
                            
                            <div class="mb-3">
                                <label for="reason" class="form-label">Reason for Breaking Bond</label>
                                <textarea class="form-control" id="reason" name="reason" rows="3" required placeholder="Please provide a reason for breaking this bond..."></textarea>
                                <div class="form-text">This information will be shared with the user.</div>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="confirmCheck" required>
                                <label class="form-check-label" for="confirmCheck">
                                    I understand that breaking this bond is permanent and cannot be undone
                                </label>
                            </div>
                            
                            <div class="d-flex justify-content-between">
                                <a th:href="@{/netcafe/application-history}" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-circle"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-danger">
                                    <i class="bi bi-scissors"></i> Break Bond
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
