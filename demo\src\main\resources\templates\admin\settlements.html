<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settlement Management - Admin Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/admin/dashboard}">
                                <i class="bi bi-house"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/admin/pending-payments}">
                                <i class="bi bi-credit-card"></i> Pending Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" th:href="@{/admin/settlements}">
                                <i class="bi bi-bank"></i> Settlements
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/admin/users}">
                                <i class="bi bi-people"></i> Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/admin/netcafe-users}">
                                <i class="bi bi-shop"></i> NetCafe Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/admin/logout}">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Settlement Management</h1>
                </div>

                <!-- Flash Messages -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card text-white bg-warning">
                            <div class="card-body">
                                <h5 class="card-title">Pending Requests</h5>
                                <h2 th:text="${pendingRequests.size()}">0</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-white bg-info">
                            <div class="card-body">
                                <h5 class="card-title">Total Requests</h5>
                                <h2 th:text="${allRequests.size()}">0</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-white bg-success">
                            <div class="card-body">
                                <h5 class="card-title">Completed Today</h5>
                                <h2>0</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pending Requests -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-clock"></i> Pending Settlement Requests
                    </div>
                    <div class="card-body">
                        <div th:if="${pendingRequests.empty}" class="text-center py-4">
                            <i class="bi bi-check-circle display-4 text-success"></i>
                            <p class="mt-3 text-muted">No pending settlement requests</p>
                        </div>

                        <div th:if="${!pendingRequests.empty}" class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Request ID</th>
                                        <th>NetCafe</th>
                                        <th>Amount</th>
                                        <th>Payment Method</th>
                                        <th>Request Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="request : ${pendingRequests}">
                                        <td th:text="${request.id}">#001</td>
                                        <td th:text="${request.netCafeUser.name}">NetCafe Name</td>
                                        <td>₹<span th:text="${request.requestedAmount}">100.00</span></td>
                                        <td>
                                            <span th:if="${request.paymentMethod == 'BANK_ACCOUNT'}" class="badge bg-info">
                                                <i class="bi bi-bank"></i> Bank
                                            </span>
                                            <span th:if="${request.paymentMethod == 'UPI'}" class="badge bg-success">
                                                <i class="bi bi-phone"></i> UPI
                                            </span>
                                        </td>
                                        <td th:text="${#temporals.format(request.requestDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</td>
                                        <td>
                                            <a th:href="@{/admin/settlements/{id}(id=${request.id})}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> Review
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- All Requests -->
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-list"></i> All Settlement Requests
                    </div>
                    <div class="card-body">
                        <div th:if="${allRequests.empty}" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="mt-3 text-muted">No settlement requests found</p>
                        </div>

                        <div th:if="${!allRequests.empty}" class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Request ID</th>
                                        <th>NetCafe</th>
                                        <th>Amount</th>
                                        <th>Payment Method</th>
                                        <th>Status</th>
                                        <th>Request Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="request : ${allRequests}">
                                        <td th:text="${request.id}">#001</td>
                                        <td th:text="${request.netCafeUser.name}">NetCafe Name</td>
                                        <td>₹<span th:text="${request.requestedAmount}">100.00</span></td>
                                        <td>
                                            <span th:if="${request.paymentMethod == 'BANK_ACCOUNT'}" class="badge bg-info">
                                                <i class="bi bi-bank"></i> Bank
                                            </span>
                                            <span th:if="${request.paymentMethod == 'UPI'}" class="badge bg-success">
                                                <i class="bi bi-phone"></i> UPI
                                            </span>
                                        </td>
                                        <td>
                                            <span th:if="${request.status == 'PENDING'}" class="badge bg-warning">
                                                <i class="bi bi-clock"></i> Pending
                                            </span>
                                            <span th:if="${request.status == 'APPROVED'}" class="badge bg-info">
                                                <i class="bi bi-check-circle"></i> Approved
                                            </span>
                                            <span th:if="${request.status == 'COMPLETED'}" class="badge bg-success">
                                                <i class="bi bi-check-circle-fill"></i> Completed
                                            </span>
                                            <span th:if="${request.status == 'REJECTED'}" class="badge bg-danger">
                                                <i class="bi bi-x-circle"></i> Rejected
                                            </span>
                                        </td>
                                        <td th:text="${#temporals.format(request.requestDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</td>
                                        <td>
                                            <a th:href="@{/admin/settlements/{id}(id=${request.id})}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
