<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #28a745;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .payment-card {
            border: 1px solid #dee2e6;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .payment-header {
            background-color: #28a745;
            color: white;
            padding: 15px;
            text-align: center;
        }
        .payment-body {
            padding: 20px;
        }
        .payment-amount {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
            text-align: center;
            margin: 20px 0;
        }
        .qr-code-container {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border: 1px dashed #dee2e6;
            border-radius: 10px;
            background-color: white;
        }
        .qr-code-img {
            max-width: 250px;
            height: auto;
        }
        .payment-instructions {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .payment-instructions ol {
            margin-bottom: 0;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Available Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/user/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2>Payment Form</h2>
                    <p>Complete your payment for the scheme application</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="payment-card">
                            <div class="payment-header">
                                <h4>Payment Details</h4>
                            </div>
                            <div class="payment-body">
                                <div class="mb-3">
                                    <p><strong>Application ID:</strong> <span th:text="${application.id}">1</span></p>
                                    <p><strong>Scheme:</strong> <span th:text="${application.scheme != null ? application.scheme.name : 'Unknown'}">Scheme Name</span></p>
                                </div>

                                <div class="payment-amount">
                                    ₹<span th:text="${application.scheme != null ? application.scheme.paymentAmount : '0.00'}">500.00</span>
                                </div>

                                <div class="qr-code-container">
                                    <img th:if="${application.scheme != null && application.scheme.paymentQrCode != null}"
                                         th:src="@{/admin/schemes/payment-qr/{id}(id=${application.scheme.id})}"
                                         alt="Payment QR Code" class="qr-code-img">
                                    <p th:if="${application.scheme == null || application.scheme.paymentQrCode == null}" class="text-muted">
                                        QR code not available
                                    </p>
                                </div>

                                <div class="payment-instructions">
                                    <h5>Payment Instructions</h5>
                                    <ol>
                                        <li>Scan the QR code using any UPI app (Google Pay, PhonePe, Paytm, etc.)</li>
                                        <li>Complete the payment for the amount shown above</li>
                                        <li>Take a screenshot of the payment confirmation</li>
                                        <li>Enter the transaction ID and upload the screenshot below</li>
                                    </ol>
                                </div>

                                <div th:if="${application.scheme != null && application.scheme.paymentDetails != null}" class="mb-3">
                                    <h5>Additional Payment Details</h5>
                                    <p th:text="${application.scheme.paymentDetails}">Payment details will be shown here.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                Submit Payment Information
                            </div>
                            <div class="card-body">
                                <form th:action="@{/user/application/{id}/submit-payment(id=${application.id})}" method="post" enctype="multipart/form-data">
                                    <div class="mb-3">
                                        <label for="transactionId" class="form-label">Transaction ID / Reference Number</label>
                                        <input type="text" class="form-control" id="transactionId" name="transactionId" required>
                                        <div class="form-text">Enter the transaction ID or reference number from your payment app</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="paymentMethod" class="form-label">Payment Method</label>
                                        <select class="form-select" id="paymentMethod" name="paymentMethod" required>
                                            <option value="UPI">UPI (Google Pay, PhonePe, Paytm, etc.)</option>
                                            <option value="BANK_TRANSFER">Bank Transfer</option>
                                            <option value="CASH">Cash</option>
                                            <option value="OTHER">Other</option>
                                        </select>
                                        <div class="form-text">Select the payment method you used</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="paymentScreenshot" class="form-label">Payment Screenshot</label>
                                        <input type="file" class="form-control" id="paymentScreenshot" name="paymentScreenshot" accept="image/*" required>
                                        <div class="form-text">Upload a screenshot of your payment confirmation</div>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <button type="submit" class="btn btn-success">Submit Payment</button>
                                        <a th:href="@{/user/application/{id}(id=${application.id})}" class="btn btn-outline-secondary">Cancel</a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
