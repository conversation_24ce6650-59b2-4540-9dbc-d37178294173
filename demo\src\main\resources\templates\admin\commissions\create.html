<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Commission</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes}">
                            <i class="bi bi-list-check"></i> Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/commissions}">
                            <i class="bi bi-cash-coin"></i> Commissions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes/applications}">
                            <i class="bi bi-file-earmark-text"></i> Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Create Commission</h2>
                    <a th:href="@{/admin/commissions}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Commissions
                    </a>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-cash-coin"></i> Commission Details
                    </div>
                    <div class="card-body">
                        <form th:action="@{/admin/commissions/create}" method="post">
                            <div class="mb-3">
                                <label for="schemeId" class="form-label">Scheme</label>
                                <select class="form-select" id="schemeId" name="schemeId" required>
                                    <option value="">Select a scheme</option>
                                    <option th:each="scheme : ${schemes}" th:value="${scheme.id}" th:text="${scheme.name}"></option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Commission Type</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="commissionType" id="fixedAmount" value="fixed" checked>
                                    <label class="form-check-label" for="fixedAmount">
                                        Fixed Amount
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="commissionType" id="percentage" value="percentage">
                                    <label class="form-check-label" for="percentage">
                                        Percentage
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3" id="fixedAmountDiv">
                                <label for="commissionAmount" class="form-label">Commission Amount</label>
                                <div class="input-group">
                                    <span class="input-group-text">₹</span>
                                    <input type="number" class="form-control" id="commissionAmount" name="commissionAmount" step="0.01" min="0">
                                </div>
                                <div class="form-text">Enter the fixed amount that NetCafe will receive for each application.</div>
                            </div>

                            <div class="mb-3 d-none" id="percentageDiv">
                                <label for="commissionPercentage" class="form-label">Commission Percentage</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="commissionPercentage" name="commissionPercentage" step="0.01" min="0" max="100">
                                    <span class="input-group-text">%</span>
                                </div>
                                <div class="form-text">Enter the percentage of the scheme payment amount that NetCafe will receive.</div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> Create Commission
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const fixedAmountRadio = document.getElementById('fixedAmount');
            const percentageRadio = document.getElementById('percentage');
            const fixedAmountDiv = document.getElementById('fixedAmountDiv');
            const percentageDiv = document.getElementById('percentageDiv');

            fixedAmountRadio.addEventListener('change', function() {
                if (this.checked) {
                    fixedAmountDiv.classList.remove('d-none');
                    percentageDiv.classList.add('d-none');
                    document.getElementById('commissionPercentage').value = '';
                }
            });

            percentageRadio.addEventListener('change', function() {
                if (this.checked) {
                    fixedAmountDiv.classList.add('d-none');
                    percentageDiv.classList.remove('d-none');
                    document.getElementById('commissionAmount').value = '';
                }
            });
        });
    </script>
</body>
</html>
