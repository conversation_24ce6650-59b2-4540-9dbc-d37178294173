<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View User Documents</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .header-card {
            background-color: #dc3545;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .document-img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .btn-back {
            background-color: #6c757d;
            color: white;
            border: none;
        }
        .btn-back:hover {
            background-color: #5a6268;
            color: white;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" href="#">
                            <i class="bi bi-people"></i> NetCafe Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" href="#">
                            <i class="bi bi-check2-circle"></i> Approvals
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" href="#">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2>User Documents</h2>
                    <p>Viewing documents for <span th:text="${user.name}">User Name</span></p>
                </div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <a th:href="@{/admin/dashboard}" class="btn btn-back">
                            <i class="bi bi-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                User Information
                            </div>
                            <div class="card-body">
                                <p><strong>Name:</strong> <span th:text="${user.name}">User Name</span></p>
                                <p><strong>Email:</strong> <span th:text="${user.email}"><EMAIL></span></p>
                                <p><strong>Mobile:</strong> <span th:text="${user.mobileNumber}">1234567890</span></p>
                                <p><strong>Aadhar Number:</strong> <span th:text="${user.aadharNumber}">123456789012</span></p>
                                <p><strong>PAN Number:</strong> <span th:text="${user.panNumber}">**********</span></p>
                                <p><strong>Registration Date:</strong> <span th:text="${#temporals.format(user.registrationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                <p><strong>Status:</strong> <span th:text="${user.approved ? 'Approved' : 'Pending Approval'}" th:class="${user.approved ? 'text-success' : 'text-warning'}">Pending</span></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                Actions
                            </div>
                            <div class="card-body">
                                <form th:if="${!user.approved}" th:action="@{/admin/approve/{id}(id=${user.id})}" method="post">
                                    <button type="submit" class="btn btn-success w-100 mb-3">
                                        <i class="bi bi-check-lg"></i> Approve Registration
                                    </button>
                                </form>
                                <a href="#" class="btn btn-primary w-100 mb-3">
                                    <i class="bi bi-printer"></i> Print Documents
                                </a>
                                <a href="#" class="btn btn-info w-100">
                                    <i class="bi bi-envelope"></i> Contact User
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                User Photo
                            </div>
                            <div class="card-body text-center">
                                <img th:src="@{/documents/photo/{id}(id=${user.id})}" alt="User Photo" class="document-img">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                CSC Certificate
                            </div>
                            <div class="card-body text-center">
                                <img th:src="@{/documents/csc-certificate/{id}(id=${user.id})}" alt="CSC Certificate" class="document-img">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                Aadhar Card
                            </div>
                            <div class="card-body text-center">
                                <img th:if="${user.aadharCardPhoto != null}" th:src="@{/documents/aadhar-card/{id}(id=${user.id})}" alt="Aadhar Card" class="document-img">
                                <p th:if="${user.aadharCardPhoto == null}" class="text-muted">No Aadhar card photo available</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                PAN Card
                            </div>
                            <div class="card-body text-center">
                                <img th:if="${user.panCardPhoto != null}" th:src="@{/documents/pan-card/{id}(id=${user.id})}" alt="PAN Card" class="document-img">
                                <p th:if="${user.panCardPhoto == null}" class="text-muted">No PAN card photo available</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
