package com.example.demo.service;

import com.example.demo.model.Message;
import com.example.demo.model.NetCafeApplication;
import com.example.demo.repository.MessageRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class MessageService {

    @Autowired
    private MessageRepository messageRepository;

    /**
     * Send a new message
     */
    @Transactional
    public Message sendMessage(NetCafeApplication netCafeApplication, String senderType, String content) {
        System.out.println("DEBUG - MessageService.sendMessage - NetCafe Application ID: " + netCafeApplication.getId());
        System.out.println("DEBUG - MessageService.sendMessage - Sender Type: " + senderType);
        System.out.println("DEBUG - MessageService.sendMessage - Content: " + content);

        Message message = new Message(netCafeApplication, senderType, content);
        Message savedMessage = messageRepository.save(message);

        System.out.println("DEBUG - MessageService.sendMessage - Message saved with ID: " + savedMessage.getId());
        return savedMessage;
    }

    /**
     * Send a new message with attachment
     */
    @Transactional
    public Message sendMessageWithAttachment(NetCafeApplication netCafeApplication, String senderType, String content,
                                           byte[] attachment, String attachmentName, String attachmentContentType,
                                           Long attachmentSize, String attachmentType) {
        System.out.println("DEBUG - MessageService.sendMessageWithAttachment - NetCafe Application ID: " + netCafeApplication.getId());
        System.out.println("DEBUG - MessageService.sendMessageWithAttachment - Sender Type: " + senderType);
        System.out.println("DEBUG - MessageService.sendMessageWithAttachment - Content: " + content);
        System.out.println("DEBUG - MessageService.sendMessageWithAttachment - Attachment Name: " + attachmentName);
        System.out.println("DEBUG - MessageService.sendMessageWithAttachment - Attachment Type: " + attachmentType);
        System.out.println("DEBUG - MessageService.sendMessageWithAttachment - Attachment Size: " + attachmentSize + " bytes");

        Message message = new Message(netCafeApplication, senderType, content,
                                     attachment, attachmentName, attachmentContentType,
                                     attachmentSize, attachmentType);
        Message savedMessage = messageRepository.save(message);

        System.out.println("DEBUG - MessageService.sendMessageWithAttachment - Message saved with ID: " + savedMessage.getId());
        return savedMessage;
    }

    /**
     * Get all messages for a NetCafeApplication
     */
    public List<Message> getMessagesByNetCafeApplication(NetCafeApplication netCafeApplication) {
        System.out.println("DEBUG - MessageService.getMessagesByNetCafeApplication - NetCafe Application ID: " + netCafeApplication.getId());

        List<Message> messages = messageRepository.findByNetCafeApplicationOrderBySentDateAsc(netCafeApplication);

        System.out.println("DEBUG - MessageService.getMessagesByNetCafeApplication - Found " + messages.size() + " messages");

        // Log each message for debugging
        for (int i = 0; i < messages.size(); i++) {
            Message message = messages.get(i);
            System.out.println("DEBUG - MessageService.getMessagesByNetCafeApplication - Message " + (i+1) +
                ": ID=" + message.getId() +
                ", Sender=" + message.getSenderType() +
                ", Content=" + message.getContent() +
                ", Date=" + message.getSentDate());
        }

        return messages;
    }

    /**
     * Get unread messages for a NetCafeApplication
     */
    public List<Message> getUnreadMessagesByNetCafeApplication(NetCafeApplication netCafeApplication) {
        return messageRepository.findByNetCafeApplicationAndIsReadFalseOrderBySentDateAsc(netCafeApplication);
    }

    /**
     * Get unread messages for a NetCafeApplication and sender type
     */
    public List<Message> getUnreadMessagesByNetCafeApplicationAndSenderType(
            NetCafeApplication netCafeApplication, String senderType) {
        return messageRepository.findByNetCafeApplicationAndSenderTypeAndIsReadFalseOrderBySentDateAsc(
                netCafeApplication, senderType);
    }

    /**
     * Mark a message as read
     */
    @Transactional
    public Message markAsRead(Long messageId) {
        Optional<Message> messageOpt = messageRepository.findById(messageId);

        if (messageOpt.isEmpty()) {
            throw new RuntimeException("Message not found with ID: " + messageId);
        }

        Message message = messageOpt.get();
        message.setRead(true);
        return messageRepository.save(message);
    }

    /**
     * Mark all messages for a NetCafeApplication as read
     */
    @Transactional
    public void markAllAsRead(NetCafeApplication netCafeApplication, String recipientType) {
        System.out.println("DEBUG - MessageService.markAllAsRead - NetCafe Application ID: " + netCafeApplication.getId());
        System.out.println("DEBUG - MessageService.markAllAsRead - Recipient Type: " + recipientType);

        // If recipientType is "USER", mark all NETCAFE messages as read
        // If recipientType is "NETCAFE", mark all USER messages as read
        String senderType = "USER".equals(recipientType) ? "NETCAFE" : "USER";
        System.out.println("DEBUG - MessageService.markAllAsRead - Marking messages from sender type: " + senderType + " as read");

        List<Message> unreadMessages = messageRepository.findByNetCafeApplicationAndSenderTypeAndIsReadFalseOrderBySentDateAsc(
                netCafeApplication, senderType);

        System.out.println("DEBUG - MessageService.markAllAsRead - Found " + unreadMessages.size() + " unread messages");

        for (Message message : unreadMessages) {
            System.out.println("DEBUG - MessageService.markAllAsRead - Marking message ID: " + message.getId() + " as read");
            message.setRead(true);
            messageRepository.save(message);
        }
    }

    /**
     * Count unread messages for a NetCafeApplication
     */
    public long countUnreadMessages(NetCafeApplication netCafeApplication) {
        return messageRepository.countByNetCafeApplicationAndIsReadFalse(netCafeApplication);
    }

    /**
     * Count unread messages for a NetCafeApplication and sender type
     */
    public long countUnreadMessages(NetCafeApplication netCafeApplication, String senderType) {
        return messageRepository.countByNetCafeApplicationAndSenderTypeAndIsReadFalse(netCafeApplication, senderType);
    }

    /**
     * Get a message by ID
     */
    public Optional<Message> getMessageById(Long id) {
        return messageRepository.findById(id);
    }

    /**
     * Get message attachment
     */
    public byte[] getMessageAttachment(Long messageId) {
        Optional<Message> messageOpt = messageRepository.findById(messageId);

        if (messageOpt.isPresent() && messageOpt.get().hasAttachment()) {
            return messageOpt.get().getAttachment();
        }

        return null;
    }
}
