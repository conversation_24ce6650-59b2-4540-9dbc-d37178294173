<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application History</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #28a745;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-completed {
            background-color: #28a745;
            color: white;
        }
        .badge-confirmed {
            background-color: #0d6efd;
            color: white;
        }
        .badge-rejected {
            background-color: #dc3545;
            color: white;
        }
        .badge-bond-broken {
            background-color: #6c757d;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .receipt-id {
            font-family: monospace;
            font-size: 0.9rem;
            font-weight: bold;
            color: #28a745;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Available Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/payments}">
                            <i class="bi bi-credit-card"></i> Payment History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/user/application-history}">
                            <i class="bi bi-clock-history"></i> Application History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Application History</h2>
                    <div>
                        <a th:href="@{/user/broken-bonds}" class="btn btn-outline-secondary">
                            <i class="bi bi-scissors"></i> View Broken Bonds
                        </a>
                    </div>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>
                <div class="alert alert-info" role="alert" th:if="${info}" th:text="${info}"></div>

                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-clock-history"></i> Completed Applications
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Scheme</th>
                                        <th>Application Date</th>
                                        <th>Completion Date</th>
                                        <th>NetCafe</th>
                                        <th>Status</th>
                                        <th>Receipt ID</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:if="${history.empty}">
                                        <td colspan="7" class="text-center">No completed applications found</td>
                                    </tr>
                                    <tr th:each="app : ${history}">
                                        <td th:text="${app.schemeName}">Scheme Name</td>
                                        <td th:text="${#temporals.format(app.applicationDate, 'dd-MM-yyyy')}">01-01-2023</td>
                                        <td th:text="${app.completionDate != null ? #temporals.format(app.completionDate, 'dd-MM-yyyy') : 'N/A'}">02-01-2023</td>
                                        <td th:text="${app.netCafeName != null ? app.netCafeName : 'N/A'}">NetCafe Name</td>
                                        <td>
                                            <span th:if="${app.finalStatus == 'PENDING'}" class="badge badge-pending">Pending</span>
                                            <span th:if="${app.finalStatus == 'COMPLETED' && app.confirmationDate == null}" class="badge badge-completed">Completed</span>
                                            <span th:if="${app.finalStatus == 'CONFIRMED'}" class="badge badge-confirmed">Confirmed</span>
                                            <span th:if="${app.finalStatus == 'REJECTED'}" class="badge badge-rejected">Rejected</span>
                                            <span th:if="${app.finalStatus == 'BOND_BROKEN'}" class="badge badge-bond-broken">Bond Broken</span>
                                        </td>
                                        <td>
                                            <span class="receipt-id" th:text="${app.completionReceiptId != null ? app.completionReceiptId : 'N/A'}">RCPT-12345678</span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a th:if="${app.finalStatus == 'COMPLETED' && app.confirmationDate == null}"
                                                   th:href="@{/user/application/confirm-completion/{receiptId}(receiptId=${app.completionReceiptId})}"
                                                   class="btn btn-sm btn-success">
                                                    <i class="bi bi-check-circle"></i> Confirm
                                                </a>
                                                <a th:if="${app.finalStatus == 'CONFIRMED'}"
                                                   th:href="@{/user/application/break-bond/{receiptId}(receiptId=${app.completionReceiptId})}"
                                                   class="btn btn-sm btn-danger">
                                                    <i class="bi bi-scissors"></i> Break Bond
                                                </a>
                                                <span th:if="${app.finalStatus == 'BOND_BROKEN'}" class="text-muted">
                                                    <i class="bi bi-scissors"></i> Bond Broken
                                                </span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card mt-4" th:if="${!history.empty && history.size() > 0}">
                    <div class="card-header">
                        <i class="bi bi-info-circle"></i> Information
                    </div>
                    <div class="card-body">
                        <p>This page shows your completed applications. When a NetCafe marks your application as completed, you'll receive a completion receipt with a unique ID.</p>
                        <p>You need to confirm the completion to finalize the process. Once confirmed, the application will be marked as confirmed and a bond is established between you and the NetCafe.</p>
                        <p>If you need to break the bond with a NetCafe, you can do so by clicking the "Break Bond" button. This will permanently end your relationship with the NetCafe for this application.</p>
                        <p>If you have any questions or concerns about a completed application, please contact the NetCafe directly or reach out to our support team.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
