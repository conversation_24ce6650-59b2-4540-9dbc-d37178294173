package com.example.demo.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "schemes")
public class Scheme {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String name;

    @Column(nullable = false, length = 2000)
    private String description;

    @Column
    private String eligibility;

    @Column
    private String benefits;

    @Column
    private String applicationProcess;

    @Column
    private String requiredDocuments;

    @Lob
    @Column(columnDefinition = "LONGBLOB")
    private byte[] schemeDocument;

    @Column
    private String documentContentType;

    @OneToMany(mappedBy = "scheme", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<SchemeDocument> schemeDocuments = new ArrayList<>();

    @Column(nullable = false)
    private LocalDateTime creationDate;

    @Column(nullable = false)
    private boolean active = true;

    @Column
    private Double paymentAmount;

    @Lob
    @Column(columnDefinition = "LONGBLOB")
    private byte[] paymentQrCode;

    @Column
    private String paymentDetails;

    // Default constructor
    public Scheme() {
        this.creationDate = LocalDateTime.now();
    }

    // Constructor with fields
    public Scheme(String name, String description) {
        this.name = name;
        this.description = description;
        this.creationDate = LocalDateTime.now();
        this.active = true;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getEligibility() {
        return eligibility;
    }

    public void setEligibility(String eligibility) {
        this.eligibility = eligibility;
    }

    public String getBenefits() {
        return benefits;
    }

    public void setBenefits(String benefits) {
        this.benefits = benefits;
    }

    public String getApplicationProcess() {
        return applicationProcess;
    }

    public void setApplicationProcess(String applicationProcess) {
        this.applicationProcess = applicationProcess;
    }

    public String getRequiredDocuments() {
        return requiredDocuments;
    }

    public void setRequiredDocuments(String requiredDocuments) {
        this.requiredDocuments = requiredDocuments;
    }

    public byte[] getSchemeDocument() {
        return schemeDocument;
    }

    public void setSchemeDocument(byte[] schemeDocument) {
        this.schemeDocument = schemeDocument;
    }

    public String getDocumentContentType() {
        return documentContentType;
    }

    public void setDocumentContentType(String documentContentType) {
        this.documentContentType = documentContentType;
    }

    public LocalDateTime getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(LocalDateTime creationDate) {
        this.creationDate = creationDate;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public List<SchemeDocument> getSchemeDocuments() {
        return schemeDocuments;
    }

    public void setSchemeDocuments(List<SchemeDocument> schemeDocuments) {
        this.schemeDocuments = schemeDocuments;
    }

    public Double getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(Double paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public byte[] getPaymentQrCode() {
        return paymentQrCode;
    }

    public void setPaymentQrCode(byte[] paymentQrCode) {
        this.paymentQrCode = paymentQrCode;
    }

    public String getPaymentDetails() {
        return paymentDetails;
    }

    public void setPaymentDetails(String paymentDetails) {
        this.paymentDetails = paymentDetails;
    }

    // Helper method to add a document requirement
    public void addDocumentRequirement(Document document, boolean required) {
        SchemeDocument schemeDocument = new SchemeDocument(this, document, required);
        this.schemeDocuments.add(schemeDocument);
    }

    // Helper method to remove a document requirement
    public void removeDocumentRequirement(Document document) {
        this.schemeDocuments.removeIf(sd -> sd.getDocument().getId().equals(document.getId()));
    }
}
