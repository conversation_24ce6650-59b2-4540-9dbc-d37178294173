package com.example.demo.controller;

import com.example.demo.model.ApplicationDocument;
import com.example.demo.model.ApplicationHistory;
import com.example.demo.model.Document;
import com.example.demo.model.GeneralUser;
import com.example.demo.model.Message;
import com.example.demo.model.NetCafeApplication;
import com.example.demo.model.PaymentRecord;
import com.example.demo.model.Scheme;
import com.example.demo.model.SchemeApplication;
import com.example.demo.service.ApplicationHistoryService;
import com.example.demo.service.GeneralUserService;
import com.example.demo.service.MessageService;
import com.example.demo.service.NetCafeApplicationService;
import com.example.demo.service.PaymentService;
import com.example.demo.service.SchemeApplicationService;
import com.example.demo.service.SchemeService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/user")
public class UserController {

    @Autowired
    private GeneralUserService generalUserService;

    @Autowired
    private SchemeService schemeService;

    @Autowired
    private SchemeApplicationService schemeApplicationService;

    @Autowired
    private NetCafeApplicationService netCafeApplicationService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private ApplicationHistoryService applicationHistoryService;

    @GetMapping("/login")
    public String showLoginForm() {
        return "user/login";
    }

    @PostMapping("/login")
    public String login(
            @RequestParam String email,
            @RequestParam String password,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        if (generalUserService.authenticateUser(email, password)) {
            GeneralUser user = generalUserService.findByEmail(email).orElseThrow();
            session.setAttribute("generalUser", user);
            return "redirect:/user/dashboard";
        }

        redirectAttributes.addFlashAttribute("error", "Invalid email or password");
        return "redirect:/user/login";
    }

    @GetMapping("/register")
    public String showRegistrationForm(Model model) {
        model.addAttribute("user", new GeneralUser());
        return "user/register";
    }

    @PostMapping("/register")
    public String registerUser(
            @ModelAttribute GeneralUser user,
            RedirectAttributes redirectAttributes) {

        // Validate input
        if (generalUserService.isEmailTaken(user.getEmail())) {
            redirectAttributes.addFlashAttribute("error", "Email is already registered");
            return "redirect:/user/register";
        }

        try {
            generalUserService.registerUser(user);
            redirectAttributes.addFlashAttribute("success", "Registration successful! Please login.");
            return "redirect:/user/login";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error during registration: " + e.getMessage());
            return "redirect:/user/register";
        }
    }

    @GetMapping("/dashboard")
    public String dashboard(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        // Get all active schemes
        List<Scheme> activeSchemes = schemeService.getAllActiveSchemes();

        // Get user's applications
        List<SchemeApplication> userApplications = schemeApplicationService.getApplicationsByUser(user);

        // Get recent payment history
        List<PaymentRecord> recentPayments = paymentService.getRecentUserPayments(user);

        model.addAttribute("user", user);
        model.addAttribute("schemes", activeSchemes);
        model.addAttribute("applications", userApplications);
        model.addAttribute("recentPayments", recentPayments);

        return "user/dashboard";
    }

    @GetMapping("/schemes")
    public String viewSchemes(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        List<Scheme> activeSchemes = schemeService.getAllActiveSchemes();
        model.addAttribute("user", user);
        model.addAttribute("schemes", activeSchemes);

        return "user/schemes";
    }

    @GetMapping("/scheme/{id}")
    public String viewSchemeDetails(
            @PathVariable Long id,
            HttpSession session,
            Model model) {

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        Optional<Scheme> schemeOpt = schemeService.getSchemeById(id);

        if (schemeOpt.isEmpty()) {
            return "redirect:/user/schemes";
        }

        Scheme scheme = schemeOpt.get();
        List<SchemeApplication> userApplications = schemeApplicationService.getApplicationsByUser(user);

        boolean hasApplied = userApplications.stream()
                .anyMatch(app -> app.getScheme().getId().equals(id));

        // Get required documents for this scheme
        List<Document> requiredDocuments = schemeService.getRequiredDocumentsForScheme(id);

        model.addAttribute("user", user);
        model.addAttribute("scheme", scheme);
        model.addAttribute("hasApplied", hasApplied);
        model.addAttribute("requiredDocuments", requiredDocuments);

        return "user/scheme-details";
    }

    @PostMapping("/apply-scheme/{id}")
    public String applyForScheme(
            @PathVariable Long id,
            @RequestParam(value = "document", required = false) MultipartFile document,
            @RequestParam(value = "documentFiles", required = false) List<MultipartFile> documentFiles,
            @RequestParam(value = "documentIds", required = false) List<Long> documentIds,
            @RequestParam(value = "preUploadedDocumentIds", required = false) List<Long> preUploadedDocumentIds,
            @RequestParam(value = "transactionId", required = false) String transactionId,
            @RequestParam(value = "paymentScreenshot", required = false) MultipartFile paymentScreenshot,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        try {
            Optional<Scheme> schemeOpt = schemeService.getSchemeById(id);

            if (schemeOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Scheme not found");
                return "redirect:/user/schemes";
            }

            Scheme scheme = schemeOpt.get();
            SchemeApplication application;

            // Check if we have individual document uploads
            if (documentFiles != null && !documentFiles.isEmpty() && documentIds != null && !documentIds.isEmpty()) {
                // Check if we have pre-uploaded documents to use
                if (preUploadedDocumentIds != null && !preUploadedDocumentIds.isEmpty()) {
                    // Use pre-uploaded documents where specified
                    application = schemeApplicationService.applyForSchemeWithPreUploadedDocuments(
                            user, scheme, document, documentFiles, documentIds, preUploadedDocumentIds);
                } else {
                    // Use regular document uploads
                    application = schemeApplicationService.applyForSchemeWithDocuments(
                            user, scheme, document, documentFiles, documentIds);
                }
            } else {
                // Fall back to the old method with a single consolidated document
                application = schemeApplicationService.applyForScheme(user, scheme, document);
            }

            // Handle payment information if provided
            if (scheme.getPaymentAmount() != null && transactionId != null && !transactionId.isEmpty()) {
                application.setTransactionId(transactionId);
                application.setPaymentStatus("PENDING"); // Set to PENDING until admin verifies

                // Save payment screenshot if provided
                if (paymentScreenshot != null && !paymentScreenshot.isEmpty()) {
                    application.setPaymentScreenshot(paymentScreenshot.getBytes());
                    application.setPaymentScreenshotContentType(paymentScreenshot.getContentType());
                }

                // Save the application first
                schemeApplicationService.saveApplication(application);

                // Record a pending payment that needs admin verification
                String remarks = "Payment for scheme application #" + application.getId() + " - Pending admin verification";
                String paymentMethod = "UPI"; // Default payment method
                paymentService.recordPendingUserPayment(application, transactionId, paymentMethod, remarks);

                System.out.println("DEBUG - Apply For Scheme - Payment recorded for verification");
            }

            redirectAttributes.addFlashAttribute("success", "Application submitted successfully. You can apply for this scheme again if needed.");
            return "redirect:/user/dashboard";
        } catch (IOException e) {
            redirectAttributes.addFlashAttribute("error", "Error uploading document: " + e.getMessage());
            return "redirect:/user/scheme/" + id;
        } catch (RuntimeException e) {
            redirectAttributes.addFlashAttribute("error", e.getMessage());
            return "redirect:/user/scheme/" + id;
        }
    }

    @GetMapping("/application-document/{applicationId}/{documentId}")
    public ResponseEntity<byte[]> getApplicationDocument(
            @PathVariable Long applicationId,
            @PathVariable Long documentId,
            HttpSession session) {

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }

        ApplicationDocument appDoc = schemeApplicationService.getApplicationDocument(applicationId, documentId);

        if (appDoc != null && appDoc.getApplication().getUser().getId().equals(user.getId())) {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(appDoc.getDocumentContentType()));
            headers.setContentDispositionFormData("attachment", appDoc.getDocumentName());
            return new ResponseEntity<>(appDoc.getDocumentFile(), headers, HttpStatus.OK);
        }

        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @GetMapping("/applications")
    public String viewApplications(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        List<SchemeApplication> applications = schemeApplicationService.getApplicationsByUser(user);

        model.addAttribute("user", user);
        model.addAttribute("applications", applications);

        return "user/applications";
    }

    @GetMapping("/application/{id}")
    public String viewApplicationDetails(
            @PathVariable Long id,
            HttpSession session,
            Model model,
            RedirectAttributes redirectAttributes) {

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        try {
            // Get the application with a fresh query to ensure we have the latest data
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/user/applications";
            }

            SchemeApplication application = applicationOpt.get();

            // Verify that this application belongs to the current user
            if (!application.getUser().getId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("error", "You don't have permission to view this application");
                return "redirect:/user/applications";
            }

            // Get required documents for this scheme if scheme exists
            List<Document> requiredDocuments = new ArrayList<>();
            if (application.getScheme() != null) {
                requiredDocuments = schemeService.getRequiredDocumentsForScheme(application.getScheme().getId());
            }

            // Check if application is assigned to a NetCafe
            boolean isAssignedToNetCafe = netCafeApplicationService.isApplicationAssigned(application.getId());
            Optional<NetCafeApplication> netCafeApplicationOpt = netCafeApplicationService.getBySchemeApplicationId(application.getId());

            // If NetCafe application exists, make sure we have the latest data
            NetCafeApplication netCafeApplication = null;
            if (netCafeApplicationOpt.isPresent()) {
                Long netCafeAppId = netCafeApplicationOpt.get().getId();
                netCafeApplicationOpt = netCafeApplicationService.getApplicationById(netCafeAppId);
                if (netCafeApplicationOpt.isPresent()) {
                    netCafeApplication = netCafeApplicationOpt.get();
                }
            }

            // Debug logging
            System.out.println("DEBUG - Application ID: " + application.getId());
            System.out.println("DEBUG - Application Status: " + application.getStatus());
            System.out.println("DEBUG - User ID: " + application.getUser().getId());
            System.out.println("DEBUG - User Name: " + application.getUser().getName());
            System.out.println("DEBUG - Scheme: " + (application.getScheme() != null ? application.getScheme().getName() : "null"));
            System.out.println("DEBUG - Documents: " + (application.getApplicationDocuments() != null ? application.getApplicationDocuments().size() : "null"));
            System.out.println("DEBUG - Is Assigned To NetCafe: " + isAssignedToNetCafe);
            System.out.println("DEBUG - NetCafe Application: " + (netCafeApplication != null ? "present (ID: " + netCafeApplication.getId() + ")" : "not present"));
            if (netCafeApplication != null) {
                System.out.println("DEBUG - NetCafe Status: " + netCafeApplication.getStatus());
                System.out.println("DEBUG - NetCafe User: " + (netCafeApplication.getNetCafeUser() != null ? netCafeApplication.getNetCafeUser().getName() : "null"));
            }

            // Get messages if application is assigned to a NetCafe
            List<Message> messages = new ArrayList<>();
            if (isAssignedToNetCafe && netCafeApplication != null) {
                messages = messageService.getMessagesByNetCafeApplication(netCafeApplication);

                // Mark all messages from NetCafe as read
                messageService.markAllAsRead(netCafeApplication, "USER");
            }

            // Check if there's a completion receipt for this application
            Optional<ApplicationHistory> historyOpt = applicationHistoryService.getApplicationHistoryBySchemeApplicationId(application.getId());
            if (historyOpt.isPresent()) {
                ApplicationHistory history = historyOpt.get();
                model.addAttribute("applicationHistory", history);
                model.addAttribute("isCompleted", true);
                model.addAttribute("isConfirmed", history.getConfirmationDate() != null);

                // Check if bond is broken
                boolean isBondBroken = false;
                if (application.getBondBroken() != null && application.getBondBroken()) {
                    isBondBroken = true;
                }
                model.addAttribute("isBondBroken", isBondBroken);
            } else {
                model.addAttribute("isCompleted", false);
                model.addAttribute("isConfirmed", false);
                model.addAttribute("isBondBroken", false);
            }

            model.addAttribute("user", user);
            model.addAttribute("application", application);
            model.addAttribute("requiredDocuments", requiredDocuments);
            model.addAttribute("isAssignedToNetCafe", isAssignedToNetCafe);
            model.addAttribute("netCafeApplication", netCafeApplication);
            model.addAttribute("messages", messages);
            model.addAttribute("newMessage", new Message());

            return "user/application-details";
        } catch (Exception e) {
            System.out.println("DEBUG - Error viewing application details: " + e.getMessage());
            e.printStackTrace();
            redirectAttributes.addFlashAttribute("error", "Error loading application: " + e.getMessage());
            return "redirect:/user/applications";
        }
    }

    @PostMapping("/application/{id}/grant-document-access")
    public String grantDocumentAccess(
            @PathVariable(required = false) Long id,
            @RequestParam(value = "applicationId", required = false) Long applicationId,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        // If path variable is null, try to use the request parameter
        if (id == null && applicationId != null) {
            id = applicationId;
            System.out.println("DEBUG - Using applicationId from form: " + id);
        }

        return processDocumentAccessGrant(id, session, redirectAttributes);
    }

    @GetMapping("/application/{id}/grant-document-access")
    public String grantDocumentAccessGet(
            @PathVariable Long id,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        System.out.println("DEBUG - GET grant document access - Application ID: " + id);

        return processDocumentAccessGrant(id, session, redirectAttributes);
    }

    @GetMapping("/application/grant-access/{id}")
    public String grantAccessSimple(
            @PathVariable Long id,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        System.out.println("DEBUG - Simple grant access - ID: " + id);

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        if (user == null) {
            return "redirect:/user/login";
        }

        try {
            // First try to get the application directly
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);
            SchemeApplication application = null;

            if (applicationOpt.isPresent()) {
                // We found a SchemeApplication with this ID
                application = applicationOpt.get();
                System.out.println("DEBUG - Found SchemeApplication with ID: " + id);
            } else {
                // Try to find a NetCafeApplication with this ID
                Optional<NetCafeApplication> netCafeApplicationOpt = netCafeApplicationService.getApplicationById(id);
                if (netCafeApplicationOpt.isPresent()) {
                    // We found a NetCafeApplication with this ID
                    NetCafeApplication netCafeApplication = netCafeApplicationOpt.get();
                    application = netCafeApplication.getApplication();
                    System.out.println("DEBUG - Found NetCafeApplication with ID: " + id + ", SchemeApplication ID: " + application.getId());
                }
            }

            if (application == null) {
                System.out.println("DEBUG - No application found with ID: " + id);
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/user/applications";
            }

            // Grant access directly
            System.out.println("DEBUG - Granting access to application ID: " + application.getId());
            application.setDocumentAccessGranted(true);
            application.setDocumentAccessGrantedDate(LocalDateTime.now());
            SchemeApplication updatedApplication = schemeApplicationService.updateApplication(application);

            System.out.println("DEBUG - Access granted. DocumentAccessGranted: " + updatedApplication.getDocumentAccessGranted());
            redirectAttributes.addFlashAttribute("success", "Document access granted successfully");
            return "redirect:/user/application/" + application.getId();
        } catch (Exception e) {
            System.out.println("DEBUG - Error granting access: " + e.getMessage());
            e.printStackTrace();
            redirectAttributes.addFlashAttribute("error", "Error: " + e.getMessage());
            return "redirect:/user/applications";
        }
    }

    @PostMapping("/application/grant-document-access")
    public String grantDocumentAccessSimple(
            @RequestParam(value = "applicationId", required = true) Long applicationId,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        System.out.println("DEBUG - Simple grant document access - Application ID: " + applicationId);

        if (applicationId == null) {
            System.out.println("DEBUG - Simple grant document access - Application ID is null");
            redirectAttributes.addFlashAttribute("error", "Application ID is missing. Please try again.");
            return "redirect:/user/applications";
        }

        return processDocumentAccessGrant(applicationId, session, redirectAttributes);
    }

    private String processDocumentAccessGrant(Long id, HttpSession session, RedirectAttributes redirectAttributes) {

        // Debug logging
        System.out.println("DEBUG - Grant Document Access - ID: " + id);

        // Check if ID is still null
        if (id == null) {
            System.out.println("DEBUG - Grant Document Access - ID is null");
            redirectAttributes.addFlashAttribute("error", "Application ID is missing. Please try again.");
            return "redirect:/user/applications";
        }

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            System.out.println("DEBUG - Grant Document Access - User is null");
            return "redirect:/user/login";
        }

        System.out.println("DEBUG - Grant Document Access - User ID: " + user.getId());
        System.out.println("DEBUG - Grant Document Access - User Name: " + user.getName());

        try {
            System.out.println("DEBUG - Grant Document Access - Getting application with ID: " + id);
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

            if (applicationOpt.isEmpty()) {
                System.out.println("DEBUG - Grant Document Access - Application not found");
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/user/applications";
            }

            SchemeApplication application = applicationOpt.get();
            System.out.println("DEBUG - Grant Document Access - Application found: " + application.getId());
            System.out.println("DEBUG - Grant Document Access - Application User ID: " + (application.getUser() != null ? application.getUser().getId() : "null"));

            // Verify that this application belongs to the current user
            if (application.getUser() == null) {
                System.out.println("DEBUG - Grant Document Access - Application user is null");
                redirectAttributes.addFlashAttribute("error", "Application user information is missing");
                return "redirect:/user/applications";
            }

            if (!application.getUser().getId().equals(user.getId())) {
                System.out.println("DEBUG - Grant Document Access - Permission denied. App User ID: " + application.getUser().getId() + ", Current User ID: " + user.getId());
                redirectAttributes.addFlashAttribute("error", "You don't have permission to update this application");
                return "redirect:/user/applications";
            }

            // Grant document access
            System.out.println("DEBUG - Grant Document Access - Setting documentAccessGranted to true");
            application.setDocumentAccessGranted(true);
            application.setDocumentAccessGrantedDate(LocalDateTime.now());

            System.out.println("DEBUG - Grant Document Access - Saving application");
            SchemeApplication updatedApplication = schemeApplicationService.updateApplication(application);

            System.out.println("DEBUG - Document access granted successfully for application ID: " + id);
            System.out.println("DEBUG - Updated application documentAccessGranted: " + updatedApplication.getDocumentAccessGranted());

            redirectAttributes.addFlashAttribute("success", "Document access granted successfully");

            // Make sure we have a valid ID for the redirect
            return "redirect:/user/application/" + id;

        } catch (Exception e) {
            System.out.println("DEBUG - Grant Document Access - Error: " + e.getMessage());
            e.printStackTrace();
            redirectAttributes.addFlashAttribute("error", "Error granting document access: " + e.getMessage());
            return "redirect:/user/applications";
        }
    }

    @GetMapping("/profile")
    public String viewProfile(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        model.addAttribute("user", user);
        return "user/profile";
    }

    @GetMapping("/edit-profile")
    public String showEditProfileForm(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        // Refresh user data from database
        Optional<GeneralUser> refreshedUser = generalUserService.findById(user.getId());
        if (refreshedUser.isPresent()) {
            user = refreshedUser.get();
            session.setAttribute("generalUser", user);
        }

        model.addAttribute("user", user);
        return "user/edit-profile";
    }

    @PostMapping("/update-profile")
    public String updateProfile(
            @ModelAttribute GeneralUser updatedUser,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        GeneralUser currentUser = (GeneralUser) session.getAttribute("generalUser");

        if (currentUser == null) {
            return "redirect:/user/login";
        }

        try {
            GeneralUser updatedUserData = generalUserService.updateUserProfile(
                    currentUser.getId(),
                    updatedUser
            );

            session.setAttribute("generalUser", updatedUserData);
            redirectAttributes.addFlashAttribute("success", "Profile updated successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error updating profile: " + e.getMessage());
        }

        return "redirect:/user/profile";
    }

    @GetMapping("/change-password")
    public String showChangePasswordForm(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        model.addAttribute("user", user);
        return "user/change-password";
    }

    @PostMapping("/update-password")
    public String updatePassword(
            @RequestParam String currentPassword,
            @RequestParam String newPassword,
            @RequestParam String confirmPassword,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        // Validate passwords
        if (!generalUserService.authenticateUser(user.getEmail(), currentPassword)) {
            redirectAttributes.addFlashAttribute("error", "Current password is incorrect");
            return "redirect:/user/change-password";
        }

        if (!newPassword.equals(confirmPassword)) {
            redirectAttributes.addFlashAttribute("error", "New passwords do not match");
            return "redirect:/user/change-password";
        }

        try {
            generalUserService.updatePassword(user.getId(), newPassword);
            redirectAttributes.addFlashAttribute("success", "Password updated successfully");
            return "redirect:/user/profile";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error updating password: " + e.getMessage());
            return "redirect:/user/change-password";
        }
    }

    @GetMapping("/application/{id}/payment")
    public String showPaymentForm(
            @PathVariable Long id,
            HttpSession session,
            Model model,
            RedirectAttributes redirectAttributes) {

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

        if (applicationOpt.isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "Application not found");
            return "redirect:/user/applications";
        }

        SchemeApplication application = applicationOpt.get();

        // Verify that this application belongs to the current user
        if (!application.getUser().getId().equals(user.getId())) {
            redirectAttributes.addFlashAttribute("error", "You don't have permission to view this application");
            return "redirect:/user/applications";
        }

        // Check if the scheme has payment information
        if (application.getScheme() == null || application.getScheme().getPaymentAmount() == null) {
            redirectAttributes.addFlashAttribute("error", "This scheme does not require payment");
            return "redirect:/user/application/" + id;
        }

        model.addAttribute("user", user);
        model.addAttribute("application", application);
        return "user/payment-form";
    }

    @PostMapping("/application/{id}/submit-payment")
    public String submitPayment(
            @PathVariable Long id,
            @RequestParam String transactionId,
            @RequestParam(value = "paymentScreenshot", required = false) MultipartFile paymentScreenshot,
            @RequestParam(value = "paymentMethod", defaultValue = "UPI") String paymentMethod,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        System.out.println("DEBUG - Submit Payment - Starting payment submission for application ID: " + id);

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            System.out.println("DEBUG - Submit Payment - User not logged in");
            return "redirect:/user/login";
        }

        System.out.println("DEBUG - Submit Payment - User ID: " + user.getId() + ", Name: " + user.getName());

        Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

        if (applicationOpt.isEmpty()) {
            System.out.println("DEBUG - Submit Payment - Application not found with ID: " + id);
            redirectAttributes.addFlashAttribute("error", "Application not found");
            return "redirect:/user/applications";
        }

        SchemeApplication application = applicationOpt.get();
        System.out.println("DEBUG - Submit Payment - Application found: " + application.getId() +
                           ", Scheme: " + (application.getScheme() != null ? application.getScheme().getName() : "null"));

        // Verify that this application belongs to the current user
        if (!application.getUser().getId().equals(user.getId())) {
            System.out.println("DEBUG - Submit Payment - Permission denied. App User ID: " +
                              application.getUser().getId() + ", Current User ID: " + user.getId());
            redirectAttributes.addFlashAttribute("error", "You don't have permission to update this application");
            return "redirect:/user/applications";
        }

        try {
            System.out.println("DEBUG - Submit Payment - Transaction ID: " + transactionId +
                              ", Payment Method: " + paymentMethod);

            // Update payment information
            application.setTransactionId(transactionId);
            application.setPaymentStatus("PENDING"); // Set to PENDING until admin verifies

            // Save payment screenshot if provided
            if (paymentScreenshot != null && !paymentScreenshot.isEmpty()) {
                System.out.println("DEBUG - Submit Payment - Screenshot provided, size: " +
                                  paymentScreenshot.getSize() + " bytes, type: " + paymentScreenshot.getContentType());
                application.setPaymentScreenshot(paymentScreenshot.getBytes());
                application.setPaymentScreenshotContentType(paymentScreenshot.getContentType());
            } else {
                System.out.println("DEBUG - Submit Payment - No screenshot provided");
            }

            // Save the application first
            SchemeApplication savedApplication = schemeApplicationService.saveApplication(application);
            System.out.println("DEBUG - Submit Payment - Application saved with ID: " + savedApplication.getId());

            // Record a pending payment that needs admin verification
            String remarks = "Payment for scheme application #" + application.getId() + " - Pending admin verification";
            PaymentRecord payment = paymentService.recordPendingUserPayment(application, transactionId, paymentMethod, remarks);
            System.out.println("DEBUG - Submit Payment - Payment record created with ID: " + payment.getId() +
                              ", Status: " + payment.getStatus() + ", Type: " + payment.getPaymentType());

            redirectAttributes.addFlashAttribute("success", "Payment information submitted successfully. Your payment is pending verification by admin.");
            return "redirect:/user/application/" + id;
        } catch (Exception e) {
            System.out.println("DEBUG - Submit Payment - Error: " + e.getMessage());
            e.printStackTrace();
            redirectAttributes.addFlashAttribute("error", "Error processing payment: " + e.getMessage());
            return "redirect:/user/application/" + id + "/payment";
        }
    }

    @GetMapping("/application-payment-screenshot/{id}")
    public ResponseEntity<byte[]> getPaymentScreenshot(@PathVariable Long id) {
        Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

        if (applicationOpt.isPresent()) {
            SchemeApplication application = applicationOpt.get();
            byte[] screenshotData = application.getPaymentScreenshot();

            if (screenshotData != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType(application.getPaymentScreenshotContentType()));
                return new ResponseEntity<>(screenshotData, headers, HttpStatus.OK);
            }
        }

        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @PostMapping("/application/{id}/send-message")
    public String sendMessage(
            @PathVariable Long id,
            @RequestParam String content,
            @RequestParam(required = false) MultipartFile attachment,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        try {
            System.out.println("DEBUG - Send Message - NetCafe Application ID: " + id);
            System.out.println("DEBUG - Send Message - Has Attachment: " + (attachment != null && !attachment.isEmpty()));

            // Get the NetCafe application directly
            Optional<NetCafeApplication> netCafeApplicationOpt = netCafeApplicationService.getApplicationById(id);

            if (netCafeApplicationOpt.isEmpty()) {
                System.out.println("DEBUG - Send Message - NetCafe Application not found");
                redirectAttributes.addFlashAttribute("error", "NetCafe Application not found");
                return "redirect:/user/applications";
            }

            NetCafeApplication netCafeApplication = netCafeApplicationOpt.get();
            System.out.println("DEBUG - Send Message - NetCafe Application found: " + netCafeApplication.getId());

            // Get the scheme application
            SchemeApplication application = netCafeApplication.getApplication();

            if (application == null) {
                System.out.println("DEBUG - Send Message - Scheme Application is null");
                redirectAttributes.addFlashAttribute("error", "Scheme Application not found");
                return "redirect:/user/applications";
            }

            System.out.println("DEBUG - Send Message - Scheme Application ID: " + application.getId());

            // Verify that this application belongs to the current user
            if (application.getUser() == null || !application.getUser().getId().equals(user.getId())) {
                System.out.println("DEBUG - Send Message - Permission denied. App User ID: " +
                    (application.getUser() != null ? application.getUser().getId() : "null") +
                    ", Current User ID: " + user.getId());
                redirectAttributes.addFlashAttribute("error", "You don't have permission to send messages for this application");
                return "redirect:/user/applications";
            }

            // No need to check for document access anymore
            // We'll allow messaging as soon as the application is assigned to a NetCafe

            // Send the message with or without attachment
            if (attachment != null && !attachment.isEmpty()) {
                // Process the attachment
                String originalFilename = attachment.getOriginalFilename();
                String contentType = attachment.getContentType();
                long fileSize = attachment.getSize();
                byte[] fileBytes = attachment.getBytes();

                // Determine attachment type
                String attachmentType = "DOCUMENT";
                if (contentType != null) {
                    if (contentType.startsWith("image/")) {
                        attachmentType = "IMAGE";
                    } else if (contentType.equals("application/pdf")) {
                        attachmentType = "PDF";
                    }
                }

                System.out.println("DEBUG - Send Message - Attachment Name: " + originalFilename);
                System.out.println("DEBUG - Send Message - Attachment Type: " + attachmentType);
                System.out.println("DEBUG - Send Message - Attachment Size: " + fileSize + " bytes");

                // Send message with attachment
                messageService.sendMessageWithAttachment(
                    netCafeApplication,
                    "USER",
                    content,
                    fileBytes,
                    originalFilename,
                    contentType,
                    fileSize,
                    attachmentType
                );

                System.out.println("DEBUG - Send Message - Message with attachment sent successfully");
            } else {
                // Send message without attachment
                System.out.println("DEBUG - Send Message - Sending message without attachment");
                messageService.sendMessage(netCafeApplication, "USER", content);
                System.out.println("DEBUG - Send Message - Message sent successfully");
            }

            redirectAttributes.addFlashAttribute("success", "Message sent successfully");
            return "redirect:/user/application/" + application.getId();
        } catch (Exception e) {
            System.out.println("DEBUG - Send Message - Error: " + e.getMessage());
            e.printStackTrace();
            redirectAttributes.addFlashAttribute("error", "Error sending message: " + e.getMessage());
            return "redirect:/user/applications";
        }
    }

    /**
     * Download message attachment
     */
    @GetMapping("/message-attachment/{id}")
    public ResponseEntity<byte[]> getMessageAttachment(@PathVariable Long id, HttpSession session) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        try {
            // Get the message
            Optional<Message> messageOpt = messageService.getMessageById(id);

            if (messageOpt.isEmpty() || !messageOpt.get().hasAttachment()) {
                return ResponseEntity.notFound().build();
            }

            Message message = messageOpt.get();
            NetCafeApplication netCafeApplication = message.getNetCafeApplication();

            // Check if the application belongs to the user
            if (netCafeApplication.getApplication() == null ||
                !netCafeApplication.getApplication().getUser().getId().equals(user.getId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Return the attachment
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(message.getAttachmentContentType()));
            headers.setContentDispositionFormData("attachment", message.getAttachmentName());
            headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

            return new ResponseEntity<>(message.getAttachment(), headers, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/application/confirm-completion/{receiptId}")
    public String showConfirmCompletionForm(@PathVariable String receiptId, HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        try {
            // Get application history by receipt ID
            Optional<ApplicationHistory> historyOpt = applicationHistoryService.getApplicationHistoryByReceiptId(receiptId);

            if (historyOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Invalid receipt ID");
                return "redirect:/user/applications";
            }

            ApplicationHistory history = historyOpt.get();

            // Verify that this application belongs to the current user
            if (!history.getUserId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("error", "You don't have permission to confirm this application");
                return "redirect:/user/applications";
            }

            // Check if already confirmed
            if (history.getConfirmationDate() != null) {
                redirectAttributes.addFlashAttribute("info", "This application has already been confirmed");
                return "redirect:/user/application-history";
            }

            model.addAttribute("history", history);
            return "user/confirm-completion";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error loading application: " + e.getMessage());
            return "redirect:/user/applications";
        }
    }

    @PostMapping("/application/confirm-completion")
    public String confirmCompletion(@RequestParam String receiptId, HttpSession session, RedirectAttributes redirectAttributes) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        try {
            // Get application history by receipt ID
            Optional<ApplicationHistory> historyOpt = applicationHistoryService.getApplicationHistoryByReceiptId(receiptId);

            if (historyOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Invalid receipt ID");
                return "redirect:/user/applications";
            }

            ApplicationHistory history = historyOpt.get();

            // Verify that this application belongs to the current user
            if (!history.getUserId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("error", "You don't have permission to confirm this application");
                return "redirect:/user/applications";
            }

            // Check if already confirmed
            if (history.getConfirmationDate() != null) {
                redirectAttributes.addFlashAttribute("info", "This application has already been confirmed");
                return "redirect:/user/application-history";
            }

            // Confirm completion
            applicationHistoryService.confirmApplicationCompletionByReceiptId(receiptId);

            redirectAttributes.addFlashAttribute("success", "Application completion confirmed successfully");
            return "redirect:/user/application-history";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error confirming application: " + e.getMessage());
            return "redirect:/user/applications";
        }
    }

    @GetMapping("/application-history")
    public String viewApplicationHistory(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        // Get application history for user
        List<ApplicationHistory> history = applicationHistoryService.getApplicationHistoryForUser(user.getId());

        // Get broken bond applications
        List<ApplicationHistory> brokenBondHistory = applicationHistoryService.getBrokenBondApplicationsForUser(user.getId());

        model.addAttribute("user", user);
        model.addAttribute("history", history);
        model.addAttribute("brokenBondHistory", brokenBondHistory);

        return "user/application-history";
    }

    @GetMapping("/broken-bonds")
    public String viewBrokenBonds(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        // Get broken bond applications
        List<ApplicationHistory> brokenBondHistory = applicationHistoryService.getBrokenBondApplicationsForUser(user.getId());

        model.addAttribute("user", user);
        model.addAttribute("brokenBondHistory", brokenBondHistory);

        return "user/broken-bonds";
    }

    @GetMapping("/application/break-bond/{receiptId}")
    public String showBreakBondForm(@PathVariable String receiptId, HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        try {
            // Get application history by receipt ID
            Optional<ApplicationHistory> historyOpt = applicationHistoryService.getApplicationHistoryByReceiptId(receiptId);

            if (historyOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Invalid receipt ID");
                return "redirect:/user/application-history";
            }

            ApplicationHistory history = historyOpt.get();

            // Verify that this application belongs to the current user
            if (!history.getUserId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("error", "You don't have permission to break this bond");
                return "redirect:/user/application-history";
            }

            // Check if bond is already broken
            Optional<SchemeApplication> schemeAppOpt = schemeApplicationService.getApplicationById(history.getSchemeApplicationId());
            if (schemeAppOpt.isPresent() && schemeAppOpt.get().getBondBroken() != null && schemeAppOpt.get().getBondBroken()) {
                redirectAttributes.addFlashAttribute("info", "This bond has already been broken");
                return "redirect:/user/application-history";
            }

            model.addAttribute("history", history);
            return "user/break-bond";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error loading application: " + e.getMessage());
            return "redirect:/user/application-history";
        }
    }

    @PostMapping("/application/break-bond")
    public String breakBond(@RequestParam String receiptId, @RequestParam String reason, HttpSession session, RedirectAttributes redirectAttributes) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        try {
            // Get application history by receipt ID
            Optional<ApplicationHistory> historyOpt = applicationHistoryService.getApplicationHistoryByReceiptId(receiptId);

            if (historyOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Invalid receipt ID");
                return "redirect:/user/application-history";
            }

            ApplicationHistory history = historyOpt.get();

            // Verify that this application belongs to the current user
            if (!history.getUserId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("error", "You don't have permission to break this bond");
                return "redirect:/user/application-history";
            }

            // Check if bond is already broken
            Optional<SchemeApplication> schemeAppOpt = schemeApplicationService.getApplicationById(history.getSchemeApplicationId());
            if (schemeAppOpt.isPresent() && schemeAppOpt.get().getBondBroken() != null && schemeAppOpt.get().getBondBroken()) {
                redirectAttributes.addFlashAttribute("info", "This bond has already been broken");
                return "redirect:/user/application-history";
            }

            // Break the bond
            applicationHistoryService.breakBondByReceiptId(receiptId, reason, "USER");

            redirectAttributes.addFlashAttribute("success", "Bond broken successfully");
            return "redirect:/user/application-history";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error breaking bond: " + e.getMessage());
            return "redirect:/user/application-history";
        }
    }

    @PostMapping("/application/{id}/confirm-completion")
    public String confirmCompletion(
            @PathVariable Long id,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        try {
            // Get the application
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/user/applications";
            }

            SchemeApplication application = applicationOpt.get();

            // Verify that this application belongs to the current user
            if (!application.getUser().getId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("error", "You don't have permission to confirm this application");
                return "redirect:/user/applications";
            }

            // Get the NetCafe application
            Optional<NetCafeApplication> netCafeApplicationOpt = netCafeApplicationService.getBySchemeApplicationId(application.getId());

            if (netCafeApplicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "NetCafe application not found");
                return "redirect:/user/application/" + id;
            }

            NetCafeApplication netCafeApplication = netCafeApplicationOpt.get();

            // Check if NetCafe has marked it as ready for completion
            if (netCafeApplication.getReadyForCompletion() == null || !netCafeApplication.getReadyForCompletion()) {
                redirectAttributes.addFlashAttribute("error", "This application is not ready for confirmation yet");
                return "redirect:/user/application/" + id;
            }

            // Check if already confirmed
            if (netCafeApplication.getUserConfirmedCompletion() != null && netCafeApplication.getUserConfirmedCompletion()) {
                redirectAttributes.addFlashAttribute("info", "You have already confirmed this application's completion");
                return "redirect:/user/application/" + id;
            }

            // Confirm completion
            netCafeApplication.setUserConfirmedCompletion(true);
            netCafeApplication.setUserConfirmationDate(LocalDateTime.now());
            netCafeApplicationService.saveApplication(netCafeApplication);

            // Automatically break the bond after user confirmation
            netCafeApplication.setBondBroken(true);
            netCafeApplication.setBondBrokenDate(LocalDateTime.now());
            netCafeApplication.setBondBreakReason("Automatic bond break after user confirmed completion");
            netCafeApplication.setBondBreakInitiator("SYSTEM");
            netCafeApplicationService.saveApplication(netCafeApplication);

            // Also update the scheme application
            application.setBondBroken(true);
            application.setBondBrokenDate(LocalDateTime.now());
            application.setBondBreakReason("Automatic bond break after user confirmed completion");
            application.setStatus("BOND_BROKEN");
            schemeApplicationService.saveApplication(application);

            // Send notification message to NetCafe
            String notificationMessage = "User has confirmed completion of the application. The bond has been automatically broken.";
            messageService.sendMessage(netCafeApplication, "USER", notificationMessage);

            redirectAttributes.addFlashAttribute("success",
                "Completion confirmed successfully. The bond has been automatically broken.");

        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error confirming completion: " + e.getMessage());
        }

        return "redirect:/user/application/" + id;
    }

    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.removeAttribute("generalUser");
        return "redirect:/";
    }
}
