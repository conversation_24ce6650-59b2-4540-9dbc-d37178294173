<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Documents - User</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #28a745;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .document-card {
            border-radius: 10px;
            transition: all 0.3s;
            margin-bottom: 20px;
        }
        .document-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .document-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .badge-verified {
            background-color: #28a745;
        }
        .badge-unverified {
            background-color: #ffc107;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/bonds}">
                            <i class="bi bi-scissors"></i> My Bonds
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/status}">
                            <i class="bi bi-graph-up"></i> Status Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/user/documents}">
                            <i class="bi bi-file-earmark"></i> My Documents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 content">
                <div class="header-card">
                    <h2><i class="bi bi-file-earmark"></i> My Documents</h2>
                    <p>Manage your personal documents for easy access during scheme applications</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <!-- Upload Button -->
                <div class="mb-4">
                    <a th:href="@{/user/documents/upload}" class="btn btn-success">
                        <i class="bi bi-upload"></i> Upload New Document
                    </a>
                </div>

                <!-- Documents List -->
                <div class="row">
                    <div th:if="${userDocuments.empty}" class="col-12">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> You haven't uploaded any documents yet. Click the "Upload New Document" button to get started.
                        </div>
                    </div>
                    
                    <div th:each="doc : ${userDocuments}" class="col-md-4">
                        <div class="card document-card">
                            <div class="card-body text-center">
                                <div class="document-icon">
                                    <i class="bi bi-file-earmark-text"></i>
                                </div>
                                <h5 class="card-title" th:text="${doc.document.name}">Document Name</h5>
                                <p class="card-text text-muted" th:text="${doc.documentName}">filename.pdf</p>
                                <p class="card-text" th:if="${doc.description}" th:text="${doc.description}">Description</p>
                                <p class="card-text">
                                    <small class="text-muted" th:text="'Uploaded: ' + ${#temporals.format(doc.uploadDate, 'dd-MM-yyyy')}">Uploaded: 01-01-2023</small>
                                </p>
                                <div class="mb-3">
                                    <span th:if="${doc.isVerified}" class="badge badge-verified">Verified</span>
                                    <span th:unless="${doc.isVerified}" class="badge badge-unverified">Pending Verification</span>
                                </div>
                                <div class="d-flex justify-content-center">
                                    <a th:href="@{'/user/documents/' + ${doc.id} + '/download'}" class="btn btn-primary me-2">
                                        <i class="bi bi-download"></i> Download
                                    </a>
                                    <form th:action="@{'/user/documents/' + ${doc.id} + '/delete'}" method="post" onsubmit="return confirm('Are you sure you want to delete this document?');">
                                        <button type="submit" class="btn btn-danger">
                                            <i class="bi bi-trash"></i> Delete
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Available Documents -->
                <div class="mt-5">
                    <h4>Available Document Types</h4>
                    <p>These are the document types you can upload to your profile:</p>
                    
                    <div class="row">
                        <div th:each="doc : ${availableDocuments}" class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h5 class="card-title" th:text="${doc.name}">Document Type</h5>
                                    <p class="card-text" th:text="${doc.description}">Document description</p>
                                    <a th:href="@{/user/documents/upload(documentId=${doc.id})}" class="btn btn-outline-success">
                                        <i class="bi bi-upload"></i> Upload
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
