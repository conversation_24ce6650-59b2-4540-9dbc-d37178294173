package com.example.demo.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "application_history")
public class ApplicationHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "scheme_application_id", nullable = false)
    private Long schemeApplicationId;

    @Column(name = "netcafe_application_id")
    private Long netCafeApplicationId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "netcafe_user_id")
    private Long netCafeUserId;

    @Column(name = "scheme_id", nullable = false)
    private Long schemeId;

    @Column(name = "scheme_name", nullable = false)
    private String schemeName;

    @Column(name = "user_name", nullable = false)
    private String userName;

    @Column(name = "netcafe_name")
    private String netCafeName;

    @Column(name = "application_date", nullable = false)
    private LocalDateTime applicationDate;

    @Column(name = "completion_date")
    private LocalDateTime completionDate;

    @Column(name = "confirmation_date")
    private LocalDateTime confirmationDate;

    @Column(name = "final_status", nullable = false)
    private String finalStatus;

    @Column(name = "payment_amount")
    private Double paymentAmount;

    @Column(name = "transaction_id")
    private String transactionId;

    @Column(name = "remarks", length = 1000)
    private String remarks;

    @Column(name = "completion_receipt_id")
    private String completionReceiptId;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "bond_broken")
    private Boolean bondBroken = false;

    @Column(name = "bond_broken_date")
    private LocalDateTime bondBrokenDate;

    @Column(name = "bond_break_reason", length = 1000)
    private String bondBreakReason;

    @Column(name = "bond_break_initiator")
    private String bondBreakInitiator; // USER or NETCAFE

    // Default constructor
    public ApplicationHistory() {
        this.createdAt = LocalDateTime.now();
    }

    // Constructor with fields
    public ApplicationHistory(SchemeApplication application, NetCafeApplication netCafeApplication, String completionReceiptId) {
        this.schemeApplicationId = application.getId();
        this.userId = application.getUser().getId();
        this.userName = application.getUser().getName();
        this.schemeId = application.getScheme().getId();
        this.schemeName = application.getScheme().getName();
        this.applicationDate = application.getApplicationDate();
        this.finalStatus = application.getStatus();
        this.paymentAmount = application.getScheme().getPaymentAmount();
        this.transactionId = application.getTransactionId();
        this.remarks = application.getRemarks();
        this.completionReceiptId = completionReceiptId;
        this.createdAt = LocalDateTime.now();

        if (netCafeApplication != null) {
            this.netCafeApplicationId = netCafeApplication.getId();
            this.netCafeUserId = netCafeApplication.getNetCafeUser().getId();
            this.netCafeName = netCafeApplication.getNetCafeUser().getName();
            this.completionDate = netCafeApplication.getCompletionDate();
        }
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSchemeApplicationId() {
        return schemeApplicationId;
    }

    public void setSchemeApplicationId(Long schemeApplicationId) {
        this.schemeApplicationId = schemeApplicationId;
    }

    public Long getNetCafeApplicationId() {
        return netCafeApplicationId;
    }

    public void setNetCafeApplicationId(Long netCafeApplicationId) {
        this.netCafeApplicationId = netCafeApplicationId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getNetCafeUserId() {
        return netCafeUserId;
    }

    public void setNetCafeUserId(Long netCafeUserId) {
        this.netCafeUserId = netCafeUserId;
    }

    public Long getSchemeId() {
        return schemeId;
    }

    public void setSchemeId(Long schemeId) {
        this.schemeId = schemeId;
    }

    public String getSchemeName() {
        return schemeName;
    }

    public void setSchemeName(String schemeName) {
        this.schemeName = schemeName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNetCafeName() {
        return netCafeName;
    }

    public void setNetCafeName(String netCafeName) {
        this.netCafeName = netCafeName;
    }

    public LocalDateTime getApplicationDate() {
        return applicationDate;
    }

    public void setApplicationDate(LocalDateTime applicationDate) {
        this.applicationDate = applicationDate;
    }

    public LocalDateTime getCompletionDate() {
        return completionDate;
    }

    public void setCompletionDate(LocalDateTime completionDate) {
        this.completionDate = completionDate;
    }

    public LocalDateTime getConfirmationDate() {
        return confirmationDate;
    }

    public void setConfirmationDate(LocalDateTime confirmationDate) {
        this.confirmationDate = confirmationDate;
    }

    public String getFinalStatus() {
        return finalStatus;
    }

    public void setFinalStatus(String finalStatus) {
        this.finalStatus = finalStatus;
    }

    public Double getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(Double paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getCompletionReceiptId() {
        return completionReceiptId;
    }

    public void setCompletionReceiptId(String completionReceiptId) {
        this.completionReceiptId = completionReceiptId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public Boolean getBondBroken() {
        return bondBroken;
    }

    public void setBondBroken(Boolean bondBroken) {
        this.bondBroken = bondBroken;
    }

    public LocalDateTime getBondBrokenDate() {
        return bondBrokenDate;
    }

    public void setBondBrokenDate(LocalDateTime bondBrokenDate) {
        this.bondBrokenDate = bondBrokenDate;
    }

    public String getBondBreakReason() {
        return bondBreakReason;
    }

    public void setBondBreakReason(String bondBreakReason) {
        this.bondBreakReason = bondBreakReason;
    }

    public String getBondBreakInitiator() {
        return bondBreakInitiator;
    }

    public void setBondBreakInitiator(String bondBreakInitiator) {
        this.bondBreakInitiator = bondBreakInitiator;
    }
}
