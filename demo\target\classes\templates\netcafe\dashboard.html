<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NetCafe Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #495057;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .welcome-card {
            background-color: #007bff;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>NetCafe Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link active" href="#">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/edit-profile}">
                            <i class="bi bi-person"></i> Edit Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/available-applications}">
                            <i class="bi bi-file-earmark-plus"></i> Available Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/payments}">
                            <i class="bi bi-credit-card"></i> Payment History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/application-history}">
                            <i class="bi bi-clock-history"></i> Application History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/bonds}">
                            <i class="bi bi-scissors"></i> Bond Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/status}">
                            <i class="bi bi-graph-up"></i> Status Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="welcome-card">
                    <h2>Welcome, <span th:text="${user.name}">User</span>!</h2>
                    <p>You are now logged into your NetCafe dashboard.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                Personal Information
                            </div>
                            <div class="card-body">
                                <p><strong>Name:</strong> <span th:text="${user.name}">User Name</span></p>
                                <p><strong>Email:</strong> <span th:text="${user.email}"><EMAIL></span></p>
                                <p><strong>Mobile:</strong> <span th:text="${user.mobileNumber}">1234567890</span></p>
                                <p><strong>Registration Date:</strong> <span th:text="${#temporals.format(user.registrationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                Document Information
                            </div>
                            <div class="card-body">
                                <p><strong>Aadhar Number:</strong> <span th:text="${user.aadharNumber}">123456789012</span></p>
                                <p><strong>PAN Number:</strong> <span th:text="${user.panNumber}">**********</span></p>
                                <p><strong>CSC Certificate:</strong> <span>Uploaded</span></p>
                                <p><strong>Photo:</strong> <span>Uploaded</span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Application Statistics -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card bg-warning text-dark h-100">
                            <div class="card-body text-center">
                                <h3 class="display-4" th:text="${pendingCount}">0</h3>
                                <h5>Pending Applications</h5>
                            </div>
                            <div class="card-footer bg-transparent border-0 text-center">
                                <a th:href="@{/netcafe/applications}" class="btn btn-sm btn-dark">View All</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white h-100">
                            <div class="card-body text-center">
                                <h3 class="display-4" th:text="${processingCount}">0</h3>
                                <h5>Processing Applications</h5>
                            </div>
                            <div class="card-footer bg-transparent border-0 text-center">
                                <a th:href="@{/netcafe/applications}" class="btn btn-sm btn-dark">View All</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white h-100">
                            <div class="card-body text-center">
                                <h3 class="display-4" th:text="${completedCount}">0</h3>
                                <h5>Completed Applications</h5>
                            </div>
                            <div class="card-footer bg-transparent border-0 text-center">
                                <a th:href="@{/netcafe/applications}" class="btn btn-sm btn-dark">View All</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-primary text-white h-100">
                            <div class="card-body text-center">
                                <h3 class="display-4" th:text="${availableCount}">0</h3>
                                <h5>Available Applications</h5>
                            </div>
                            <div class="card-footer bg-transparent border-0 text-center">
                                <a th:href="@{/netcafe/available-applications}" class="btn btn-sm btn-light">Claim New</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Statistics -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card bg-warning text-dark h-100">
                            <div class="card-header bg-warning text-dark">
                                <i class="bi bi-cash-stack me-2"></i> Payment Summary
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Unsettled Amount</h5>
                                        <h3 class="text-primary">₹<span th:text="${unsettledAmount != null ? unsettledAmount : '0.00'}">0.00</span></h3>
                                        <p class="text-muted">Pending settlement from admin</p>
                                    </div>
                                    <div class="col-md-6">
                                        <h5>Total Earnings</h5>
                                        <h3 class="text-success">₹<span th:text="${totalEarnings != null ? totalEarnings : '0.00'}">0.00</span></h3>
                                        <p class="text-muted">All-time earnings</p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer bg-transparent border-0">
                                <a th:href="@{/netcafe/payments}" class="btn btn-dark">
                                    <i class="bi bi-credit-card"></i> View Payment History
                                </a>
                                <a th:href="@{/netcafe/payments/unsettled}" class="btn btn-outline-dark ms-2">
                                    <i class="bi bi-cash"></i> View Unsettled Payments
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header">
                                <i class="bi bi-info-circle me-2"></i> Commission Information
                            </div>
                            <div class="card-body">
                                <p><strong>How commissions work:</strong></p>
                                <ul>
                                    <li>You earn a commission for each application you process</li>
                                    <li>Commission rates are set by the admin for each scheme</li>
                                    <li>Commissions are settled on a weekly basis</li>
                                    <li>Payment is made directly to your registered bank account</li>
                                </ul>
                                <p><strong>To maximize your earnings:</strong></p>
                                <ul>
                                    <li>Process applications promptly</li>
                                    <li>Ensure all documents are properly verified</li>
                                    <li>Maintain good communication with applicants</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Applications -->
                <div class="card">
                    <div class="card-header">
                        Recent Applications
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Scheme</th>
                                        <th>Applicant</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="app, iterStat : ${pendingApplications}" th:if="${iterStat.index < 5}">
                                        <td th:text="${app.application.id}">1</td>
                                        <td th:text="${app.application.scheme != null ? app.application.scheme.name : 'Unknown'}">Scheme Name</td>
                                        <td th:text="${app.application.user != null ? app.application.user.name : 'Unknown'}">Applicant Name</td>
                                        <td>
                                            <span class="badge badge-pending">Pending</span>
                                        </td>
                                        <td>
                                            <a th:href="@{/netcafe/application/{id}(id=${app.id})}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    <tr th:if="${pendingApplications.empty}">
                                        <td colspan="5" class="text-center">No pending applications found</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a th:href="@{/netcafe/applications}" class="btn btn-primary">
                                <i class="bi bi-list"></i> View All Applications
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Available Applications -->
                <div class="card mt-4" th:if="${!availableApplications.empty}">
                    <div class="card-header bg-primary text-white">
                        <i class="bi bi-file-earmark-plus me-2"></i> Available Applications
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Scheme</th>
                                        <th>Applicant</th>
                                        <th>Application Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="app, iterStat : ${availableApplications}" th:if="${iterStat.index < 3}">
                                        <td th:text="${app.id}">1</td>
                                        <td th:text="${app.scheme != null ? app.scheme.name : 'Unknown'}">Scheme Name</td>
                                        <td th:text="${app.user != null ? app.user.name : 'Unknown'}">Applicant Name</td>
                                        <td th:text="${#temporals.format(app.applicationDate, 'dd-MM-yyyy')}">01-01-2023</td>
                                        <td>
                                            <form th:action="@{/netcafe/claim-application/{id}(id=${app.id})}" method="post" style="display: inline;">
                                                <button type="submit" class="btn btn-sm btn-success">
                                                    <i class="bi bi-check-circle"></i> Claim
                                                </button>
                                            </form>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a th:href="@{/netcafe/available-applications}" class="btn btn-primary">
                                <i class="bi bi-list"></i> View All Available Applications
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card mt-4">
                    <div class="card-header">
                        Quick Actions
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/netcafe/edit-profile}" class="btn btn-primary w-100">
                                    <i class="bi bi-pencil-square"></i> Edit Profile
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/netcafe/applications}" class="btn btn-success w-100">
                                    <i class="bi bi-file-earmark-text"></i> My Applications
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/netcafe/available-applications}" class="btn btn-info w-100">
                                    <i class="bi bi-file-earmark-plus"></i> Available Applications
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/netcafe/payments}" class="btn btn-warning w-100">
                                    <i class="bi bi-credit-card"></i> Payment History
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/netcafe/application-history}" class="btn btn-secondary w-100">
                                    <i class="bi bi-clock-history"></i> Application History
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/netcafe/bonds}" class="btn btn-dark w-100">
                                    <i class="bi bi-scissors"></i> Bond Management
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a th:href="@{/netcafe/status}" class="btn btn-primary w-100">
                                    <i class="bi bi-graph-up"></i> Status Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
