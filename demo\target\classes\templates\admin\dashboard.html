<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .welcome-card {
            background-color: #dc3545;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .btn-approve {
            background-color: #28a745;
            color: white;
            border: none;
        }
        .btn-approve:hover {
            background-color: #218838;
            color: white;
        }
        .btn-view {
            background-color: #17a2b8;
            color: white;
            border: none;
        }
        .btn-view:hover {
            background-color: #138496;
            color: white;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>

                    <!-- User Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/users}">
                            <i class="bi bi-person"></i> General Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/netcafe-users}">
                            <i class="bi bi-people"></i> NetCafe Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/netcafe-approvals}">
                            <i class="bi bi-check2-circle"></i> NetCafe Approvals
                        </a>
                    </li>

                    <!-- Scheme Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes}">
                            <i class="bi bi-list-check"></i> Manage Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes/applications}">
                            <i class="bi bi-file-earmark-text"></i> Scheme Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/bonds}">
                            <i class="bi bi-scissors"></i> Bond Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/status}">
                            <i class="bi bi-graph-up"></i> Status Dashboard
                        </a>
                    </li>

                    <!-- Payment Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/pending-payments}">
                            <i class="bi bi-credit-card"></i> Verify Payments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/commissions}">
                            <i class="bi bi-cash-coin"></i> NetCafe Commissions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/documents}">
                            <i class="bi bi-file-earmark"></i> Document Types
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/user-documents}">
                            <i class="bi bi-file-earmark-check"></i> User Documents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/pending-payments}">
                            <i class="bi bi-credit-card"></i> Pending Payments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" href="#">
                            <i class="bi bi-gear"></i> Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="welcome-card">
                    <h2>Welcome, <span th:text="${admin.name}">Admin</span>!</h2>
                    <p>You are now logged into the admin dashboard.</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>Pending Approvals</span>
                        <span class="badge badge-pending" th:text="${pendingApprovals.size()} + ' pending'">0 pending</span>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Mobile</th>
                                        <th>Aadhar Number</th>
                                        <th>PAN Number</th>
                                        <th>Registration Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:if="${pendingApprovals.empty}">
                                        <td colspan="8" class="text-center">No pending approvals</td>
                                    </tr>
                                    <tr th:each="user : ${pendingApprovals}">
                                        <td th:text="${user.id}">1</td>
                                        <td th:text="${user.name}">John Doe</td>
                                        <td th:text="${user.email}"><EMAIL></td>
                                        <td th:text="${user.mobileNumber}">1234567890</td>
                                        <td th:text="${user.aadharNumber}">123456789012</td>
                                        <td th:text="${user.panNumber}">**********</td>
                                        <td th:text="${#temporals.format(user.registrationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</td>
                                        <td>
                                            <div class="d-flex">
                                                <a th:href="@{/admin/view-documents/{id}(id=${user.id})}" class="btn btn-view btn-sm me-2">
                                                    <i class="bi bi-eye"></i> View Documents
                                                </a>
                                                <form th:action="@{/admin/approve/{id}(id=${user.id})}" method="post">
                                                    <button type="submit" class="btn btn-approve btn-sm">
                                                        <i class="bi bi-check-lg"></i> Approve
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                Quick Actions
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <a th:href="@{/admin/schemes}" class="btn btn-danger w-100">
                                            <i class="bi bi-list-check"></i> Manage Schemes
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a th:href="@{/admin/schemes/create}" class="btn btn-danger w-100">
                                            <i class="bi bi-plus-circle"></i> Create New Scheme
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a th:href="@{/admin/commissions}" class="btn btn-danger w-100">
                                            <i class="bi bi-cash-coin"></i> Manage Commissions
                                        </a>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <a th:href="@{/admin/schemes/applications}" class="btn btn-danger w-100">
                                            <i class="bi bi-file-earmark-text"></i> Scheme Applications
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a th:href="@{/admin/documents}" class="btn btn-outline-danger w-100">
                                            <i class="bi bi-file-earmark"></i> Manage Documents
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a th:href="@{/admin/user-documents}" class="btn btn-outline-danger w-100">
                                            <i class="bi bi-file-earmark-check"></i> User Documents
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a th:href="@{/admin/bonds}" class="btn btn-outline-danger w-100">
                                            <i class="bi bi-scissors"></i> Manage Bonds
                                        </a>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <a th:href="@{/admin/status}" class="btn btn-outline-primary w-100">
                                            <i class="bi bi-graph-up"></i> Status Dashboard
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        Quick Stats
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- NetCafe Users Stats -->
                            <div class="col-md-4 mb-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Total NetCafe Users</h5>
                                        <h2 class="display-4" th:text="${totalNetCafeUsers}">0</h2>
                                        <p class="mb-0">
                                            <span class="badge bg-light text-dark" th:text="${approvedNetCafeUsers} + ' approved'">0 approved</span>
                                        </p>
                                        <a th:href="@{/admin/netcafe-users}" class="btn btn-sm btn-light mt-2">Manage Users</a>
                                    </div>
                                </div>
                            </div>

                            <!-- General Users Stats -->
                            <div class="col-md-4 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">General Users</h5>
                                        <h2 class="display-4" th:text="${totalGeneralUsers}">0</h2>
                                        <p class="mb-0">
                                            <span class="badge bg-light text-dark" th:text="${activeGeneralUsers} + ' active'">0 active</span>
                                        </p>
                                        <a th:href="@{/admin/users}" class="btn btn-sm btn-light mt-2">Manage Users</a>
                                    </div>
                                </div>
                            </div>

                            <!-- Applications Stats -->
                            <div class="col-md-4 mb-3">
                                <div class="card bg-warning text-dark">
                                    <div class="card-body">
                                        <h5 class="card-title">Applications</h5>
                                        <h2 class="display-4" th:text="${totalApplications}">0</h2>
                                        <p class="mb-0">
                                            <span class="badge bg-light text-dark" th:text="${pendingApplications} + ' pending'">0 pending</span>
                                        </p>
                                        <a th:href="@{/admin/schemes/applications}" class="btn btn-sm btn-dark mt-2">View Applications</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Second Row for Additional Stats -->
                        <div class="row mt-3">
                            <!-- Pending Approvals -->
                            <div class="col-md-4 mb-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Pending Approvals</h5>
                                        <h2 class="display-4" th:text="${pendingApprovals.size()}">0</h2>
                                        <a th:href="@{/admin/netcafe-approvals}" class="btn btn-sm btn-light mt-2">Review Approvals</a>
                                    </div>
                                </div>
                            </div>

                            <!-- NetCafe Applications -->
                            <div class="col-md-4 mb-3">
                                <div class="card bg-secondary text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">NetCafe Assignments</h5>
                                        <h2 class="display-4" th:text="${totalNetCafeApplications}">0</h2>
                                        <p class="mb-0">
                                            <span class="badge bg-light text-dark" th:text="${completedNetCafeApplications} + ' completed'">0 completed</span>
                                        </p>
                                        <a th:href="@{/admin/netcafe-applications}" class="btn btn-sm btn-light mt-2">View Assignments</a>
                                    </div>
                                </div>
                            </div>

                            <!-- Payments -->
                            <div class="col-md-4 mb-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body">
                                        <h5 class="card-title">Payments</h5>
                                        <p class="mb-2">Verify user payments and manage commissions</p>
                                        <a th:href="@{/admin/pending-payments}" class="btn btn-sm btn-light">Verify Payments</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
