<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #28a745;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .section-title {
            color: #28a745;
            border-bottom: 2px solid #28a745;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-approved {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-rejected {
            background-color: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Available Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/user/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2>Application Details</h2>
                    <p>View details of your scheme application</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-header">
                        Application Information
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class="section-title">Application Status</h5>
                                <p><strong>Application ID:</strong> <span th:text="${application.id}">1</span></p>
                                <p><strong>Application Date:</strong> <span th:text="${#temporals.format(application.applicationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                <p>
                                    <strong>Status:</strong>
                                    <span th:if="${application.status == 'PENDING'}" class="badge badge-pending">Pending</span>
                                    <span th:if="${application.status == 'APPROVED'}" class="badge badge-approved">Approved</span>
                                    <span th:if="${application.status == 'REJECTED'}" class="badge badge-rejected">Rejected</span>
                                </p>
                                <p th:if="${application.remarks != null && !application.remarks.isEmpty()}">
                                    <strong>Remarks:</strong> <span th:text="${application.remarks}">Remarks</span>
                                </p>
                                <p th:if="${application.scheme != null && application.scheme.paymentAmount != null}">
                                    <strong>Payment Amount:</strong> ₹<span th:text="${application.scheme.paymentAmount}">500.00</span>
                                </p>
                                <p th:if="${application.paymentStatus != null}">
                                    <strong>Payment Status:</strong>
                                    <span th:if="${application.paymentStatus == 'PENDING'}" class="badge badge-pending">Pending</span>
                                    <span th:if="${application.paymentStatus == 'COMPLETED'}" class="badge badge-approved">Completed</span>
                                    <span th:if="${application.paymentStatus == 'VERIFIED'}" class="badge badge-approved">Verified</span>
                                </p>
                                <p th:if="${application.transactionId != null && !application.transactionId.isEmpty()}">
                                    <strong>Transaction ID:</strong> <span th:text="${application.transactionId}">TXN123456</span>
                                </p>

                                <!-- NetCafe Status Section -->
                                <div th:if="${isAssignedToNetCafe}" class="mt-3">
                                    <h5 class="section-title">NetCafe Processing Status</h5>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <p><strong>NetCafe Name:</strong> <span th:text="${netCafeApplication.netCafeUser.name}">NetCafe Name</span></p>
                                            <p th:if="${netCafeApplication.netCafeUser.shopName != null}">
                                                <strong>Shop Name:</strong> <span th:text="${netCafeApplication.netCafeUser.shopName}">Shop Name</span>
                                            </p>
                                            <p>
                                                <strong>Status:</strong>
                                                <span th:if="${netCafeApplication.status == 'PENDING'}" class="badge badge-pending">Pending</span>
                                                <span th:if="${netCafeApplication.status == 'PROCESSING'}" class="badge badge-processing">Processing</span>
                                                <span th:if="${netCafeApplication.status == 'COMPLETED'}" class="badge badge-completed">Completed</span>
                                            </p>
                                            <p th:if="${netCafeApplication.remarks != null && !netCafeApplication.remarks.isEmpty()}">
                                                <strong>Remarks:</strong> <span th:text="${netCafeApplication.remarks}">Remarks</span>
                                            </p>

                                            <!-- Document Access Status -->
                                            <div th:if="${application.documentAccessGranted}">
                                                <div class="alert alert-success">
                                                    <i class="bi bi-check-circle"></i> Document access automatically granted to NetCafe when they claimed your application
                                                    <br><small class="text-muted">Granted on: <span th:text="${#temporals.format(application.documentAccessGrantedDate, 'dd-MM-yyyy HH:mm')}"></span></small>
                                                </div>
                                            </div>
                                            <div th:if="${!application.documentAccessGranted}">
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i> Document access will be automatically granted when a NetCafe claims your application
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5 class="section-title">Scheme Information</h5>
                                <div th:if="${application.scheme != null}">
                                    <p><strong>Scheme Name:</strong> <span th:text="${application.scheme.name}">Scheme Name</span></p>
                                    <p><strong>Scheme Description:</strong> <span th:text="${application.scheme.description != null ? #strings.abbreviate(application.scheme.description, 150) : 'No description available'}">Scheme description...</span></p>
                                    <a th:href="@{/user/scheme/{id}(id=${application.scheme.id})}" class="btn btn-sm btn-outline-success">
                                        <i class="bi bi-info-circle"></i> View Scheme Details
                                    </a>
                                </div>
                                <div th:if="${application.scheme == null}" class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle"></i> Scheme information not available
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h5 class="section-title">Submitted Documents</h5>

                                <!-- Consolidated document (old method) -->
                                <div th:if="${application.supportingDocument != null}" class="mb-3">
                                    <p><strong>Supporting Document:</strong></p>
                                    <a th:href="@{/admin/schemes/applications/document/{id}(id=${application.id})}"
                                       class="btn btn-sm btn-outline-primary" target="_blank">
                                        <i class="bi bi-file-earmark"></i> View Document
                                    </a>
                                </div>

                                <!-- Individual document uploads -->
                                <div th:if="${application.applicationDocuments != null && !application.applicationDocuments.empty}" class="mb-3">
                                    <div class="table-responsive">
                                        <table class="table table-bordered">
                                            <thead class="table-light">
                                                <tr>
                                                    <th>Document Type</th>
                                                    <th>File Name</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr th:each="appDoc : ${application.applicationDocuments}">
                                                    <td th:text="${appDoc.document != null ? appDoc.document.name : 'Unknown'}">Document Name</td>
                                                    <td th:text="${appDoc.documentName != null ? appDoc.documentName : 'Unknown'}">file.pdf</td>
                                                    <td>
                                                        <a th:if="${appDoc.document != null}"
                                                           th:href="@{/user/application-document/{appId}/{docId}(appId=${application.id},docId=${appDoc.document.id})}"
                                                           class="btn btn-sm btn-outline-primary" target="_blank">
                                                            <i class="bi bi-eye"></i> View
                                                        </a>
                                                        <span th:if="${appDoc.document == null}" class="text-muted">Not available</span>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div th:if="${application.supportingDocument == null && (application.applicationDocuments == null || application.applicationDocuments.empty)}" class="alert alert-info">
                                    <i class="bi bi-info-circle"></i> No documents were submitted with this application.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Section -->
                <div class="row mt-4" th:if="${application.scheme != null && application.scheme.paymentAmount != null && (application.paymentStatus == null || application.paymentStatus == 'PENDING')}">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <i class="bi bi-exclamation-triangle me-2"></i> Payment Required
                            </div>
                            <div class="card-body">
                                <p>This scheme requires a payment of ₹<span th:text="${application.scheme.paymentAmount}">500.00</span> to complete your application.</p>
                                <a th:href="@{/user/application/{id}/payment(id=${application.id})}" class="btn btn-success">
                                    <i class="bi bi-credit-card"></i> Make Payment
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Screenshot Section -->
                <div class="row mt-4" th:if="${application.paymentStatus != null && application.paymentStatus != 'PENDING' && application.paymentScreenshot != null}">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                Payment Screenshot
                            </div>
                            <div class="card-body text-center">
                                <img th:src="@{/user/application-payment-screenshot/{id}(id=${application.id})}"
                                     alt="Payment Screenshot" class="img-fluid" style="max-height: 300px;">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <a th:href="@{/user/applications}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Applications
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
