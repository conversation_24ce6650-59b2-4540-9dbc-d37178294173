package com.example.demo.model;

import jakarta.persistence.*;

@Entity
@Table(name = "application_documents")
public class ApplicationDocument {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "application_id", nullable = false)
    private SchemeApplication application;

    @ManyToOne
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;

    @Lob
    @Column(columnDefinition = "LONGBLOB")
    private byte[] documentFile;

    @Column
    private String documentContentType;

    @Column
    private String documentName;

    // Default constructor
    public ApplicationDocument() {
    }

    // Constructor with fields
    public ApplicationDocument(SchemeApplication application, Document document, byte[] documentFile, 
                              String documentContentType, String documentName) {
        this.application = application;
        this.document = document;
        this.documentFile = documentFile;
        this.documentContentType = documentContentType;
        this.documentName = documentName;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public SchemeApplication getApplication() {
        return application;
    }

    public void setApplication(SchemeApplication application) {
        this.application = application;
    }

    public Document getDocument() {
        return document;
    }

    public void setDocument(Document document) {
        this.document = document;
    }

    public byte[] getDocumentFile() {
        return documentFile;
    }

    public void setDocumentFile(byte[] documentFile) {
        this.documentFile = documentFile;
    }

    public String getDocumentContentType() {
        return documentContentType;
    }

    public void setDocumentContentType(String documentContentType) {
        this.documentContentType = documentContentType;
    }

    public String getDocumentName() {
        return documentName;
    }

    public void setDocumentName(String documentName) {
        this.documentName = documentName;
    }
}
