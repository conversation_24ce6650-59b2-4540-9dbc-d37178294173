<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - View Messages</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            min-height: 100vh;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #495057;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 0;
            padding: 20px;
        }
        .welcome-card {
            background-color: #007bff;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .card {
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
        }
        .message-bubble {
            border-radius: 15px;
            padding: 10px 15px;
            margin-bottom: 10px;
            max-width: 80%;
            display: inline-block;
            word-break: break-word;
        }
        .message-container {
            margin-bottom: 15px;
        }
        .user-message {
            background-color: #e9ecef;
            color: #212529;
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
            border-bottom-right-radius: 15px;
            border-bottom-left-radius: 5px;
            float: left;
        }
        .netcafe-message {
            background-color: #007bff;
            color: white;
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
            border-bottom-right-radius: 5px;
            border-bottom-left-radius: 15px;
            float: right;
        }
        .message-header {
            font-size: 0.85rem;
            margin-bottom: 5px;
        }
        .message-content {
            font-size: 1rem;
        }
        .message-time {
            font-size: 0.75rem;
            color: #6c757d;
            margin-top: 5px;
            text-align: right;
        }
        .messages-container {
            max-height: 500px;
            overflow-y: auto;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }
        @media (min-width: 768px) {
            .content {
                margin-left: 16.666667%;
            }
        }
        @media (max-width: 767.98px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: auto;
                z-index: 1000;
                padding-top: 0;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>

                    <!-- User Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/users}">
                            <i class="bi bi-person"></i> General Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/netcafe-users}">
                            <i class="bi bi-people"></i> NetCafe Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/netcafe-approvals}">
                            <i class="bi bi-check2-circle"></i> NetCafe Approvals
                        </a>
                    </li>

                    <!-- Scheme Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes}">
                            <i class="bi bi-list-check"></i> Manage Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/schemes/applications}">
                            <i class="bi bi-file-earmark-text"></i> Scheme Applications
                        </a>
                    </li>

                    <!-- Payment Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/pending-payments}">
                            <i class="bi bi-credit-card"></i> Verify Payments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/commissions}">
                            <i class="bi bi-cash-coin"></i> NetCafe Commissions
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="welcome-card">
                    <h2>Conversation History</h2>
                    <p>View messages between user and NetCafe for this application</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>Application Details</span>
                                    <a th:href="@{/admin/schemes/applications/{id}(id=${application.id})}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-arrow-left"></i> Back to Application
                                    </a>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Application ID:</strong> <span th:text="${application.id}">1</span></p>
                                        <p><strong>Scheme:</strong> <span th:text="${application.scheme != null ? application.scheme.name : 'N/A'}">Scheme Name</span></p>
                                        <p><strong>Status:</strong>
                                            <span th:if="${application.status == 'PENDING'}" class="badge bg-warning">Pending</span>
                                            <span th:if="${application.status == 'APPROVED'}" class="badge bg-success">Approved</span>
                                            <span th:if="${application.status == 'REJECTED'}" class="badge bg-danger">Rejected</span>
                                            <span th:if="${application.status == 'COMPLETED'}" class="badge bg-primary">Completed</span>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>User:</strong> <span th:text="${application.user != null ? application.user.name : 'N/A'}">User Name</span></p>
                                        <p><strong>NetCafe User:</strong> <span th:text="${netCafeApplication.netCafeUser != null ? netCafeApplication.netCafeUser.name : 'N/A'}">NetCafe Name</span></p>
                                        <p><strong>Bond Status:</strong>
                                            <span th:if="${netCafeApplication.bondBroken == true}" class="badge bg-danger">Broken</span>
                                            <span th:unless="${netCafeApplication.bondBroken == true}" class="badge bg-success">Active</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <i class="bi bi-chat-dots me-2"></i> Message History
                            </div>
                            <div class="card-body">
                                <div class="messages-container">
                                    <div th:if="${messages != null && !messages.empty}">
                                        <div th:each="message : ${messages}" class="message-container clearfix">
                                            <div th:class="${message.senderType == 'USER' ? 'message-bubble user-message' : 'message-bubble netcafe-message'}">
                                                <div class="message-header">
                                                    <strong th:text="${message.senderType == 'USER' ? (application.user != null ? application.user.name : 'User') : (netCafeApplication.netCafeUser != null ? netCafeApplication.netCafeUser.name : 'NetCafe')}">Sender</strong>
                                                    <span class="badge" th:classappend="${message.senderType == 'USER' ? 'bg-secondary' : 'bg-primary'}" th:text="${message.senderType}">USER</span>
                                                </div>
                                                <div class="message-content" th:text="${message.content}">Message content</div>
                                                <div class="message-time" th:text="${message.sentDate != null ? #temporals.format(message.sentDate, 'dd-MM-yyyy HH:mm') : ''}">Date</div>

                                                <!-- Attachment display -->
                                                <div th:if="${message.attachment != null && message.attachmentName != null && message.attachmentContentType != null}" class="mt-2 border-top pt-2">
                                                    <div class="d-flex align-items-center">
                                                        <i class="bi bi-paperclip me-2"></i>
                                                        <span th:if="${message.attachmentType == 'IMAGE'}" class="badge bg-info me-2">Image</span>
                                                        <span th:if="${message.attachmentType == 'PDF'}" class="badge bg-danger me-2">PDF</span>
                                                        <span th:if="${message.attachmentType == 'DOCUMENT'}" class="badge bg-secondary me-2">Document</span>
                                                        <a th:href="@{/admin/message-attachment/{id}(id=${message.id})}" target="_blank" class="text-decoration-none">
                                                            <span th:text="${message.attachmentName}">attachment.pdf</span>
                                                            <small class="text-muted ms-2" th:text="${message.attachmentSize != null ? (#numbers.formatDecimal(message.attachmentSize / 1024, 0, 2) + ' KB') : ''}">123 KB</small>
                                                        </a>
                                                    </div>
                                                    <!-- Preview for images -->
                                                    <div th:if="${message.attachmentType == 'IMAGE'}" class="mt-2">
                                                        <a th:href="@{/admin/message-attachment/{id}(id=${message.id})}" target="_blank">
                                                            <img th:src="@{/admin/message-attachment/{id}(id=${message.id})}" class="img-thumbnail" style="max-height: 150px;" alt="Image attachment">
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div th:if="${messages == null || messages.empty}" class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> No messages found between the user and NetCafe for this application.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
