/* Common Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: transform 0.3s;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    font-weight: bold;
    border-bottom: none;
}

/* Button Styles */
.btn-primary {
    background-color: #007bff;
    border: none;
}

.btn-primary:hover {
    background-color: #0069d9;
}

.btn-danger {
    background-color: #dc3545;
    border: none;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-success {
    background-color: #28a745;
    border: none;
}

.btn-success:hover {
    background-color: #218838;
}

/* Form Styles */
.form-label {
    font-weight: 500;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
}

/* Hero Section */
.hero-section {
    background-color: #007bff;
    color: white;
    padding: 60px 0;
    margin-bottom: 40px;
}

/* Footer */
.footer {
    background-color: #343a40;
    color: white;
    padding: 20px 0;
    margin-top: 40px;
}

/* Sidebar */
.sidebar {
    background-color: #343a40;
    color: white;
    height: 100vh;
    position: fixed;
    padding-top: 20px;
}

.sidebar-link {
    color: #adb5bd;
    text-decoration: none;
    display: block;
    padding: 10px 15px;
    transition: all 0.3s;
}

.sidebar-link:hover, .sidebar-link.active {
    color: white;
    background-color: #495057;
}

.sidebar-link i {
    margin-right: 10px;
}

.content {
    margin-left: 250px;
    padding: 20px;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }
    .content {
        margin-left: 0;
    }
}
