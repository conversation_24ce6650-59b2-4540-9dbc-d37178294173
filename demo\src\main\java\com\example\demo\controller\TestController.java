package com.example.demo.controller;

import com.example.demo.model.GeneralUser;
import com.example.demo.service.GeneralUserService;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Controller
@RequestMapping("/test")
public class TestController {

    @Autowired
    private GeneralUserService generalUserService;

    @GetMapping("/user-info")
    @ResponseBody
    public Map<String, Object> getUserInfo(HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        
        if (user == null) {
            result.put("error", "No user in session");
            return result;
        }
        
        result.put("id", user.getId());
        result.put("name", user.getName());
        result.put("email", user.getEmail());
        result.put("mobileNumber", user.getMobileNumber());
        result.put("address", user.getAddress());
        result.put("registrationDate", user.getRegistrationDate());
        result.put("active", user.isActive());
        
        return result;
    }
    
    @GetMapping("/user-from-db")
    @ResponseBody
    public Map<String, Object> getUserFromDb(HttpSession session) {
        Map<String, Object> result = new HashMap<>();
        
        GeneralUser sessionUser = (GeneralUser) session.getAttribute("generalUser");
        
        if (sessionUser == null) {
            result.put("error", "No user in session");
            return result;
        }
        
        Optional<GeneralUser> dbUser = generalUserService.findById(sessionUser.getId());
        
        if (dbUser.isEmpty()) {
            result.put("error", "User not found in database");
            return result;
        }
        
        GeneralUser user = dbUser.get();
        
        result.put("id", user.getId());
        result.put("name", user.getName());
        result.put("email", user.getEmail());
        result.put("mobileNumber", user.getMobileNumber());
        result.put("address", user.getAddress());
        result.put("registrationDate", user.getRegistrationDate());
        result.put("active", user.isActive());
        
        return result;
    }
    
    @GetMapping("/profile-test")
    public String profileTest(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        
        if (user == null) {
            return "redirect:/user/login";
        }
        
        model.addAttribute("user", user);
        return "user/profile-test";
    }
}
