<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Payments</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #28a745;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-completed {
            background-color: #28a745;
            color: white;
        }
        .badge-failed {
            background-color: #dc3545;
            color: white;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Available Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/user/payments}">
                            <i class="bi bi-credit-card"></i> Payment History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2>Application Payments</h2>
                    <div>
                        <a th:href="@{/user/payments}" class="btn btn-outline-secondary me-2">
                            <i class="bi bi-arrow-left"></i> Back to Payment History
                        </a>
                        <a th:href="@{/user/application/{id}(id=${application.id})}" class="btn btn-outline-primary">
                            <i class="bi bi-file-earmark-text"></i> View Application
                        </a>
                    </div>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <!-- Application Details -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-file-earmark-text"></i> Application Details
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Application ID:</strong> <span th:text="${application.id}">1</span></p>
                                <p><strong>Scheme:</strong> <span th:text="${application.scheme != null ? application.scheme.name : 'N/A'}">Scheme Name</span></p>
                                <p><strong>Status:</strong> <span th:text="${application.status}">Pending</span></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Submitted On:</strong> <span th:text="${#temporals.format(application.createdAt, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                <p><strong>Payment Amount:</strong> ₹<span th:text="${application.scheme != null && application.scheme.paymentAmount != null ? application.scheme.paymentAmount : 'N/A'}">500.00</span></p>
                                <p><strong>Transaction ID:</strong> <span th:text="${application.transactionId != null ? application.transactionId : 'Not provided'}">TXN123456</span></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment History Table -->
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-credit-card"></i> Payment Records
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Transaction ID</th>
                                        <th>Payment Method</th>
                                        <th>Status</th>
                                        <th>Remarks</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:if="${payments.empty}">
                                        <td colspan="6" class="text-center">No payment records found</td>
                                    </tr>
                                    <tr th:each="payment : ${payments}">
                                        <td th:text="${#temporals.format(payment.paymentDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</td>
                                        <td>₹<span th:text="${payment.amount}">500.00</span></td>
                                        <td th:text="${payment.transactionId != null ? payment.transactionId : 'N/A'}">TXN123456</td>
                                        <td th:text="${payment.paymentMethod != null ? payment.paymentMethod : 'N/A'}">UPI</td>
                                        <td>
                                            <span th:if="${payment.status == 'PENDING'}" class="badge badge-pending">Pending</span>
                                            <span th:if="${payment.status == 'COMPLETED'}" class="badge badge-completed">Completed</span>
                                            <span th:if="${payment.status == 'FAILED'}" class="badge badge-failed">Failed</span>
                                        </td>
                                        <td th:text="${payment.remarks != null ? payment.remarks : 'N/A'}">Payment for application</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Make Payment Section (if payment is pending) -->
                <div class="card mt-4" th:if="${application.paymentStatus == null || application.paymentStatus == 'PENDING'}">
                    <div class="card-header">
                        <i class="bi bi-cash"></i> Make Payment
                    </div>
                    <div class="card-body">
                        <form th:action="@{/user/application/{id}/payment(id=${application.id})}" method="post" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="transactionId" class="form-label">Transaction ID</label>
                                <input type="text" class="form-control" id="transactionId" name="transactionId" required>
                                <div class="form-text">Enter the transaction ID from your payment method</div>
                            </div>
                            <div class="mb-3">
                                <label for="paymentScreenshot" class="form-label">Payment Screenshot</label>
                                <input type="file" class="form-control" id="paymentScreenshot" name="paymentScreenshot" accept="image/*">
                                <div class="form-text">Upload a screenshot of your payment confirmation (optional)</div>
                            </div>
                            <button type="submit" class="btn btn-primary">Submit Payment</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
