<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settlement History - NetCafe Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/netcafe/dashboard}">
                                <i class="bi bi-house"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/netcafe/applications}">
                                <i class="bi bi-file-text"></i> Applications
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/netcafe/payments}">
                                <i class="bi bi-credit-card"></i> Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" th:href="@{/netcafe/settlement}">
                                <i class="bi bi-bank"></i> Settlement
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" th:href="@{/netcafe/logout}">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Settlement History</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a th:href="@{/netcafe/settlement/request}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> New Settlement Request
                        </a>
                    </div>
                </div>

                <!-- Flash Messages -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Available Balance Card -->
                <div class="card mb-4">
                    <div class="card-header">
                        <i class="bi bi-wallet2"></i> Available Balance
                    </div>
                    <div class="card-body">
                        <h3 class="text-success">₹<span th:text="${availableBalance}">0.00</span></h3>
                        <p class="text-muted">Available for settlement</p>
                    </div>
                </div>

                <!-- Settlement Requests Table -->
                <div class="card">
                    <div class="card-header">
                        <i class="bi bi-list"></i> Settlement Requests
                    </div>
                    <div class="card-body">
                        <div th:if="${settlementRequests.empty}" class="text-center py-4">
                            <i class="bi bi-inbox display-4 text-muted"></i>
                            <p class="mt-3 text-muted">No settlement requests found.</p>
                            <a th:href="@{/netcafe/settlement/request}" class="btn btn-primary">
                                <i class="bi bi-plus"></i> Create First Settlement Request
                            </a>
                        </div>

                        <div th:if="${!settlementRequests.empty}" class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Request ID</th>
                                        <th>Amount</th>
                                        <th>Payment Method</th>
                                        <th>Status</th>
                                        <th>Request Date</th>
                                        <th>Response Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="request : ${settlementRequests}">
                                        <td th:text="${request.id}">#001</td>
                                        <td>₹<span th:text="${request.requestedAmount}">100.00</span></td>
                                        <td>
                                            <span th:if="${request.paymentMethod == 'BANK_ACCOUNT'}" class="badge bg-info">
                                                <i class="bi bi-bank"></i> Bank Account
                                            </span>
                                            <span th:if="${request.paymentMethod == 'UPI'}" class="badge bg-success">
                                                <i class="bi bi-phone"></i> UPI
                                            </span>
                                        </td>
                                        <td>
                                            <span th:if="${request.status == 'PENDING'}" class="badge bg-warning">
                                                <i class="bi bi-clock"></i> Pending
                                            </span>
                                            <span th:if="${request.status == 'APPROVED'}" class="badge bg-info">
                                                <i class="bi bi-check-circle"></i> Approved
                                            </span>
                                            <span th:if="${request.status == 'COMPLETED'}" class="badge bg-success">
                                                <i class="bi bi-check-circle-fill"></i> Completed
                                            </span>
                                            <span th:if="${request.status == 'REJECTED'}" class="badge bg-danger">
                                                <i class="bi bi-x-circle"></i> Rejected
                                            </span>
                                        </td>
                                        <td th:text="${#temporals.format(request.requestDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</td>
                                        <td>
                                            <span th:if="${request.adminResponseDate != null}" 
                                                  th:text="${#temporals.format(request.adminResponseDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span>
                                            <span th:if="${request.adminResponseDate == null}" class="text-muted">-</span>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    data-bs-toggle="modal" th:data-bs-target="'#detailsModal' + ${request.id}">
                                                <i class="bi bi-eye"></i> View
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Details Modals -->
                <div th:each="request : ${settlementRequests}" class="modal fade" th:id="'detailsModal' + ${request.id}" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Settlement Request Details - #<span th:text="${request.id}">001</span></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6>Request Information</h6>
                                        <p><strong>Amount:</strong> ₹<span th:text="${request.requestedAmount}">100.00</span></p>
                                        <p><strong>Available Balance:</strong> ₹<span th:text="${request.availableAmount}">150.00</span></p>
                                        <p><strong>Status:</strong> <span th:text="${request.status}">PENDING</span></p>
                                        <p><strong>Request Date:</strong> <span th:text="${#temporals.format(request.requestDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>Payment Details</h6>
                                        <p><strong>Method:</strong> <span th:text="${request.paymentMethod}">BANK_ACCOUNT</span></p>
                                        
                                        <div th:if="${request.paymentMethod == 'BANK_ACCOUNT'}">
                                            <p><strong>Account Holder:</strong> <span th:text="${request.accountHolderName}">John Doe</span></p>
                                            <p><strong>Account Number:</strong> <span th:text="${request.bankAccountNumber}">**********</span></p>
                                            <p><strong>IFSC Code:</strong> <span th:text="${request.ifscCode}">SBIN0001234</span></p>
                                            <p><strong>Bank Name:</strong> <span th:text="${request.bankName}">State Bank of India</span></p>
                                        </div>
                                        
                                        <div th:if="${request.paymentMethod == 'UPI'}">
                                            <p><strong>UPI ID:</strong> <span th:text="${request.upiId}">user@paytm</span></p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div th:if="${request.adminRemarks != null}" class="mt-3">
                                    <h6>Admin Remarks</h6>
                                    <p th:text="${request.adminRemarks}">Admin remarks here</p>
                                </div>
                                
                                <div th:if="${request.rejectionReason != null}" class="mt-3">
                                    <h6>Rejection Reason</h6>
                                    <p class="text-danger" th:text="${request.rejectionReason}">Rejection reason here</p>
                                </div>
                                
                                <div th:if="${request.transactionId != null}" class="mt-3">
                                    <h6>Transaction Details</h6>
                                    <p><strong>Transaction ID:</strong> <span th:text="${request.transactionId}">TXN123456</span></p>
                                    <p><strong>Completion Date:</strong> <span th:text="${#temporals.format(request.completionDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
