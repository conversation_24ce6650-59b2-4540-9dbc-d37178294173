package com.example.demo.repository;

import com.example.demo.model.NetCafeApplication;
import com.example.demo.model.NetCafeUser;
import com.example.demo.model.SchemeApplication;

import org.springframework.data.jdbc.repository.query.Query;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface NetCafeApplicationRepository extends JpaRepository<NetCafeApplication, Long> {

    List<NetCafeApplication> findByNetCafeUser(NetCafeUser netCafeUser);

    List<NetCafeApplication> findByApplication(SchemeApplication application);

    List<NetCafeApplication> findByNetCafeUserAndStatus(NetCafeUser netCafeUser, String status);

    // Find the latest NetCafeApplication for a SchemeApplication
    @Query("SELECT na FROM NetCafeApplication na WHERE na.application.id = :applicationId ORDER BY na.id DESC")
    List<NetCafeApplication> findByApplicationIdOrderByIdDesc(Long applicationId);

    boolean existsByApplication_Id(Long applicationId);

    long countByStatus(String status);
}
