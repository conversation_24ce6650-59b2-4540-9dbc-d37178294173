package com.example.demo.controller;

import com.example.demo.model.Admin;
import com.example.demo.model.GeneralUser;
import com.example.demo.model.NetCafeApplication;
import com.example.demo.model.NetCafeUser;
import com.example.demo.model.SchemeApplication;
import com.example.demo.service.NetCafeApplicationService;
import com.example.demo.service.SchemeApplicationService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
public class StatusController {

    @Autowired
    private SchemeApplicationService schemeApplicationService;

    @Autowired
    private NetCafeApplicationService netCafeApplicationService;

    // Admin Status Dashboard
    @GetMapping("/admin/status")
    public String adminStatusDashboard(HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        Admin admin = (Admin) session.getAttribute("admin");
        if (admin == null) {
            return "redirect:/admin/login";
        }
        
        try {
            // Get all applications
            List<SchemeApplication> allApplications = schemeApplicationService.getAllApplications();
            
            // Count applications by status
            Map<String, Integer> statusCounts = new HashMap<>();
            statusCounts.put("PENDING", 0);
            statusCounts.put("APPROVED", 0);
            statusCounts.put("REJECTED", 0);
            statusCounts.put("COMPLETED", 0);
            statusCounts.put("BOND_BROKEN", 0);
            statusCounts.put("TOTAL", allApplications.size());
            
            // Count NetCafe applications by status
            Map<String, Integer> netCafeStatusCounts = new HashMap<>();
            netCafeStatusCounts.put("PENDING", 0);
            netCafeStatusCounts.put("PROCESSING", 0);
            netCafeStatusCounts.put("COMPLETED", 0);
            netCafeStatusCounts.put("REJECTED", 0);
            netCafeStatusCounts.put("TOTAL", 0);
            
            // Count applications by status
            for (SchemeApplication app : allApplications) {
                String status = app.getStatus();
                if (status != null) {
                    statusCounts.put(status, statusCounts.getOrDefault(status, 0) + 1);
                }
            }
            
            // Get all NetCafe applications
            List<NetCafeApplication> allNetCafeApplications = netCafeApplicationService.getAllApplications();
            netCafeStatusCounts.put("TOTAL", allNetCafeApplications.size());
            
            // Count NetCafe applications by status
            for (NetCafeApplication app : allNetCafeApplications) {
                String status = app.getStatus();
                if (status != null) {
                    netCafeStatusCounts.put(status, netCafeStatusCounts.getOrDefault(status, 0) + 1);
                }
            }
            
            // Calculate unclaimed applications
            int unclaimedCount = 0;
            for (SchemeApplication app : allApplications) {
                if (app.getStatus().equals("APPROVED") && !netCafeApplicationService.isApplicationAssigned(app.getId())) {
                    unclaimedCount++;
                }
            }
            
            model.addAttribute("admin", admin);
            model.addAttribute("statusCounts", statusCounts);
            model.addAttribute("netCafeStatusCounts", netCafeStatusCounts);
            model.addAttribute("unclaimedCount", unclaimedCount);
            model.addAttribute("recentApplications", allApplications.size() > 5 ? allApplications.subList(0, 5) : allApplications);
            
            return "admin/status/index";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error loading status dashboard: " + e.getMessage());
            return "redirect:/admin/dashboard";
        }
    }
    
    // User Status Dashboard
    @GetMapping("/user/status")
    public String userStatusDashboard(HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");
        if (user == null) {
            return "redirect:/user/login";
        }
        
        try {
            // Get user's applications
            List<SchemeApplication> userApplications = schemeApplicationService.getAllApplicationsByUser(user);
            
            // Count applications by status
            Map<String, Integer> statusCounts = new HashMap<>();
            statusCounts.put("PENDING", 0);
            statusCounts.put("APPROVED", 0);
            statusCounts.put("REJECTED", 0);
            statusCounts.put("COMPLETED", 0);
            statusCounts.put("BOND_BROKEN", 0);
            statusCounts.put("TOTAL", userApplications.size());
            
            // Count applications by status
            for (SchemeApplication app : userApplications) {
                String status = app.getStatus();
                if (status != null) {
                    statusCounts.put(status, statusCounts.getOrDefault(status, 0) + 1);
                }
            }
            
            // Count applications by NetCafe status
            Map<String, Integer> netCafeStatusCounts = new HashMap<>();
            netCafeStatusCounts.put("PENDING", 0);
            netCafeStatusCounts.put("PROCESSING", 0);
            netCafeStatusCounts.put("COMPLETED", 0);
            netCafeStatusCounts.put("REJECTED", 0);
            netCafeStatusCounts.put("UNCLAIMED", 0);
            
            // Check NetCafe status for each application
            for (SchemeApplication app : userApplications) {
                if (app.getStatus().equals("APPROVED")) {
                    if (netCafeApplicationService.isApplicationAssigned(app.getId())) {
                        // Get NetCafe application status
                        NetCafeApplication netCafeApp = netCafeApplicationService.getBySchemeApplicationId(app.getId()).orElse(null);
                        if (netCafeApp != null) {
                            String status = netCafeApp.getStatus();
                            if (status != null) {
                                netCafeStatusCounts.put(status, netCafeStatusCounts.getOrDefault(status, 0) + 1);
                            }
                        }
                    } else {
                        // Application is approved but not claimed by any NetCafe
                        netCafeStatusCounts.put("UNCLAIMED", netCafeStatusCounts.getOrDefault("UNCLAIMED", 0) + 1);
                    }
                }
            }
            
            model.addAttribute("user", user);
            model.addAttribute("statusCounts", statusCounts);
            model.addAttribute("netCafeStatusCounts", netCafeStatusCounts);
            model.addAttribute("recentApplications", userApplications.size() > 5 ? userApplications.subList(0, 5) : userApplications);
            
            return "user/status/index";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error loading status dashboard: " + e.getMessage());
            return "redirect:/user/dashboard";
        }
    }
    
    // NetCafe Status Dashboard
    @GetMapping("/netcafe/status")
    public String netcafeStatusDashboard(HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        NetCafeUser netCafeUser = (NetCafeUser) session.getAttribute("netCafeUser");
        if (netCafeUser == null) {
            return "redirect:/netcafe/login";
        }
        
        try {
            // Get NetCafe's applications
            List<NetCafeApplication> netCafeApplications = netCafeApplicationService.getAllApplicationsByNetCafeUser(netCafeUser);
            
            // Count applications by status
            Map<String, Integer> statusCounts = new HashMap<>();
            statusCounts.put("PENDING", 0);
            statusCounts.put("PROCESSING", 0);
            statusCounts.put("COMPLETED", 0);
            statusCounts.put("REJECTED", 0);
            statusCounts.put("TOTAL", netCafeApplications.size());
            
            // Count applications by status
            for (NetCafeApplication app : netCafeApplications) {
                String status = app.getStatus();
                if (status != null) {
                    statusCounts.put(status, statusCounts.getOrDefault(status, 0) + 1);
                }
            }
            
            // Get available applications that can be claimed
            List<SchemeApplication> availableApplications = schemeApplicationService.getAvailableApplicationsForNetCafe();
            
            model.addAttribute("netCafeUser", netCafeUser);
            model.addAttribute("statusCounts", statusCounts);
            model.addAttribute("availableCount", availableApplications.size());
            model.addAttribute("recentApplications", netCafeApplications.size() > 5 ? netCafeApplications.subList(0, 5) : netCafeApplications);
            
            return "netcafe/status/index";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error loading status dashboard: " + e.getMessage());
            return "redirect:/netcafe/dashboard";
        }
    }
}
