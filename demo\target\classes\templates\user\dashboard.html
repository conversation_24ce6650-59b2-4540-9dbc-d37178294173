<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #28a745;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .welcome-card {
            background-color: #28a745;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .scheme-card {
            height: 100%;
        }
        .scheme-card .card-body {
            display: flex;
            flex-direction: column;
        }
        .scheme-card .btn {
            margin-top: auto;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
        }
        .badge-approved {
            background-color: #28a745;
            color: white;
        }
        .badge-rejected {
            background-color: #dc3545;
            color: white;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link active" href="#">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Available Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/payments}">
                            <i class="bi bi-credit-card"></i> Payment History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/application-history}">
                            <i class="bi bi-clock-history"></i> Application History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/bonds}">
                            <i class="bi bi-scissors"></i> My Bonds
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/status}">
                            <i class="bi bi-graph-up"></i> Status Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/documents}">
                            <i class="bi bi-file-earmark"></i> My Documents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="welcome-card">
                    <h2>Welcome, <span th:text="${user.name}">User</span>!</h2>
                    <p>Explore and apply for various government schemes</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span>My Applications</span>
                                <a th:href="@{/user/applications}" class="btn btn-sm btn-outline-success">View All</a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Scheme</th>
                                                <th>Date Applied</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr th:if="${applications.empty}">
                                                <td colspan="3" class="text-center">No applications yet</td>
                                            </tr>
                                            <tr th:each="app, iterStat : ${applications}" th:if="${iterStat.index < 5}">
                                                <td th:text="${app.scheme.name}">Scheme Name</td>
                                                <td th:text="${#temporals.format(app.applicationDate, 'dd-MM-yyyy')}">01-01-2023</td>
                                                <td>
                                                    <span th:if="${app.status == 'PENDING'}" class="badge badge-pending">Pending</span>
                                                    <span th:if="${app.status == 'APPROVED'}" class="badge badge-approved">Approved</span>
                                                    <span th:if="${app.status == 'REJECTED'}" class="badge badge-rejected">Rejected</span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                Quick Actions
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <a th:href="@{/user/schemes}" class="btn btn-success w-100">
                                            <i class="bi bi-search"></i> Browse Schemes
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a th:href="@{/user/applications}" class="btn btn-outline-success w-100">
                                            <i class="bi bi-file-earmark-text"></i> My Applications
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a th:href="@{/user/payments}" class="btn btn-outline-success w-100">
                                            <i class="bi bi-credit-card"></i> Payment History
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a th:href="@{/user/profile}" class="btn btn-outline-success w-100">
                                            <i class="bi bi-person"></i> Edit Profile
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a th:href="@{/user/application-history}" class="btn btn-outline-success w-100">
                                            <i class="bi bi-clock-history"></i> Application History
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a th:href="@{/user/bonds}" class="btn btn-outline-success w-100">
                                            <i class="bi bi-scissors"></i> My Bonds
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a th:href="@{/user/status}" class="btn btn-outline-primary w-100">
                                            <i class="bi bi-graph-up"></i> Status Dashboard
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <a th:href="@{/user/documents}" class="btn btn-outline-success w-100">
                                            <i class="bi bi-file-earmark"></i> My Documents
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <h4 class="mb-3">Featured Schemes</h4>
                <div class="row">
                    <div class="col-md-4 mb-4" th:each="scheme, iterStat : ${schemes}" th:if="${iterStat.index < 3}">
                        <div class="card scheme-card">
                            <div class="card-body">
                                <h5 class="card-title" th:text="${scheme.name}">Scheme Name</h5>
                                <p class="card-text" th:text="${#strings.abbreviate(scheme.description, 100)}">Scheme description...</p>
                                <a th:href="@{/user/scheme/{id}(id=${scheme.id})}" class="btn btn-success">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
