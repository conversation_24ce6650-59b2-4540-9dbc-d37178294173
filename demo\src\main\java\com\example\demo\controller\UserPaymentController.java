package com.example.demo.controller;

import com.example.demo.model.PaymentRecord;
import com.example.demo.model.SchemeApplication;
import com.example.demo.model.GeneralUser;
import com.example.demo.service.PaymentService;
import com.example.demo.service.SchemeApplicationService;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/user/payments")
public class UserPaymentController {

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private SchemeApplicationService schemeApplicationService;

    @GetMapping
    public String viewPaymentHistory(HttpSession session, Model model) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        List<PaymentRecord> payments = paymentService.getUserPaymentHistory(user);
        model.addAttribute("user", user);
        model.addAttribute("payments", payments);

        return "user/payment-history";
    }

    @GetMapping("/application/{id}")
    public String viewApplicationPayments(@PathVariable Long id, HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

        if (applicationOpt.isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "Application not found");
            return "redirect:/user/applications";
        }

        SchemeApplication application = applicationOpt.get();

        // Verify that this application belongs to the current user
        if (!application.getUser().getId().equals(user.getId())) {
            redirectAttributes.addFlashAttribute("error", "You don't have permission to view this application's payments");
            return "redirect:/user/applications";
        }

        List<PaymentRecord> payments = paymentService.getPaymentsForApplication(application);

        model.addAttribute("user", user);
        model.addAttribute("application", application);
        model.addAttribute("payments", payments);

        return "user/application-payments";
    }

    @GetMapping("/filter")
    public String filterPayments(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            HttpSession session,
            Model model) {

        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        List<PaymentRecord> payments;

        if (startDate != null && !startDate.isEmpty() && endDate != null && !endDate.isEmpty()) {
            LocalDateTime start = LocalDate.parse(startDate).atStartOfDay();
            LocalDateTime end = LocalDate.parse(endDate).atTime(LocalTime.MAX);

            payments = paymentService.getPaymentsByDateRange(start, end);
            // Filter to only show this user's payments
            payments = payments.stream()
                .filter(p -> p.getUser() != null && p.getUser().getId().equals(user.getId()))
                .toList();
        } else {
            payments = paymentService.getUserPaymentHistory(user);
        }

        model.addAttribute("user", user);
        model.addAttribute("payments", payments);
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);

        return "user/payment-history";
    }

    @GetMapping("/receipt/{id}")
    public String viewPaymentReceipt(@PathVariable Long id, HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        GeneralUser user = (GeneralUser) session.getAttribute("generalUser");

        if (user == null) {
            return "redirect:/user/login";
        }

        try {
            PaymentRecord payment = paymentService.getPaymentById(id)
                    .orElseThrow(() -> new RuntimeException("Payment not found with ID: " + id));

            // Verify that this payment belongs to the current user
            if (payment.getUser() == null || !payment.getUser().getId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("error", "You don't have permission to view this payment receipt");
                return "redirect:/user/payments";
            }

            model.addAttribute("payment", payment);
            model.addAttribute("user", user);

            return "user/payment-receipt";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error retrieving payment receipt: " + e.getMessage());
            return "redirect:/user/payments";
        }
    }
}
