package com.example.demo.service;

import com.example.demo.model.NetCafeCommission;
import com.example.demo.model.Scheme;
import com.example.demo.repository.NetCafeCommissionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
public class NetCafeCommissionService {

    @Autowired
    private NetCafeCommissionRepository netCafeCommissionRepository;

    /**
     * Create a new commission rate for a scheme
     */
    @Transactional
    public NetCafeCommission createCommission(Scheme scheme, BigDecimal amount, BigDecimal percentage, boolean isFixedAmount, String createdBy) {
        // Deactivate any existing commission for this scheme
        Optional<NetCafeCommission> existingCommission = netCafeCommissionRepository.findBySchemeAndIsActiveTrue(scheme);
        existingCommission.ifPresent(commission -> {
            commission.setIsActive(false);
            netCafeCommissionRepository.save(commission);
        });
        
        // Create new commission
        NetCafeCommission commission = new NetCafeCommission();
        commission.setScheme(scheme);
        commission.setCommissionAmount(amount);
        commission.setCommissionPercentage(percentage);
        commission.setIsFixedAmount(isFixedAmount);
        commission.setIsActive(true);
        commission.setCreatedBy(createdBy);
        commission.setCreatedAt(LocalDateTime.now());
        commission.setUpdatedAt(LocalDateTime.now());
        
        return netCafeCommissionRepository.save(commission);
    }

    /**
     * Update an existing commission
     */
    @Transactional
    public NetCafeCommission updateCommission(Long id, BigDecimal amount, BigDecimal percentage, boolean isFixedAmount) {
        Optional<NetCafeCommission> commissionOpt = netCafeCommissionRepository.findById(id);
        
        if (commissionOpt.isEmpty()) {
            throw new RuntimeException("Commission not found with ID: " + id);
        }
        
        NetCafeCommission commission = commissionOpt.get();
        commission.setCommissionAmount(amount);
        commission.setCommissionPercentage(percentage);
        commission.setIsFixedAmount(isFixedAmount);
        commission.setUpdatedAt(LocalDateTime.now());
        
        return netCafeCommissionRepository.save(commission);
    }

    /**
     * Deactivate a commission
     */
    @Transactional
    public void deactivateCommission(Long id) {
        Optional<NetCafeCommission> commissionOpt = netCafeCommissionRepository.findById(id);
        
        if (commissionOpt.isEmpty()) {
            throw new RuntimeException("Commission not found with ID: " + id);
        }
        
        NetCafeCommission commission = commissionOpt.get();
        commission.setIsActive(false);
        commission.setUpdatedAt(LocalDateTime.now());
        
        netCafeCommissionRepository.save(commission);
    }

    /**
     * Get active commission for a scheme
     */
    public Optional<NetCafeCommission> getActiveCommissionForScheme(Scheme scheme) {
        return netCafeCommissionRepository.findBySchemeAndIsActiveTrue(scheme);
    }

    /**
     * Get active commission for a scheme by ID
     */
    public Optional<NetCafeCommission> getActiveCommissionForSchemeById(Long schemeId) {
        return netCafeCommissionRepository.findActiveCommissionBySchemeId(schemeId);
    }

    /**
     * Get all active commissions
     */
    public List<NetCafeCommission> getAllActiveCommissions() {
        return netCafeCommissionRepository.findByIsActiveTrueOrderByScheme_NameAsc();
    }

    /**
     * Get commission history for a scheme
     */
    public List<NetCafeCommission> getCommissionHistoryForScheme(Scheme scheme) {
        return netCafeCommissionRepository.findBySchemeOrderByCreatedAtDesc(scheme);
    }

    /**
     * Calculate commission amount for a scheme and payment amount
     */
    public BigDecimal calculateCommissionForScheme(Scheme scheme, BigDecimal paymentAmount) {
        Optional<NetCafeCommission> commissionOpt = netCafeCommissionRepository.findBySchemeAndIsActiveTrue(scheme);
        
        if (commissionOpt.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        NetCafeCommission commission = commissionOpt.get();
        
        if (commission.getIsFixedAmount()) {
            return commission.getCommissionAmount();
        } else {
            // Calculate percentage-based commission
            return paymentAmount.multiply(commission.getCommissionPercentage().divide(new BigDecimal("100")));
        }
    }
}
