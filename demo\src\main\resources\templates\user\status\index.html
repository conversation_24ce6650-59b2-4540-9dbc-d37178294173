<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Application Status Dashboard - User</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #28a745;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .status-card {
            border-radius: 10px;
            margin-bottom: 15px;
            transition: all 0.3s;
        }
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .status-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .status-count {
            font-size: 2rem;
            font-weight: bold;
        }
        .status-label {
            font-size: 1rem;
            color: #6c757d;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/bonds}">
                            <i class="bi bi-scissors"></i> My Bonds
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/user/status}">
                            <i class="bi bi-graph-up"></i> Status Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 content">
                <div class="header-card">
                    <h2><i class="bi bi-graph-up"></i> My Application Status Dashboard</h2>
                    <p>Overview of all your application statuses</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <!-- Application Status Cards -->
                <h4 class="mb-3">My Applications Status</h4>
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card status-card text-center bg-primary text-white">
                            <div class="card-body">
                                <div class="status-icon"><i class="bi bi-file-earmark-text"></i></div>
                                <div class="status-count" th:text="${statusCounts.TOTAL}">0</div>
                                <div class="status-label">Total Applications</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card status-card text-center bg-warning text-dark">
                            <div class="card-body">
                                <div class="status-icon"><i class="bi bi-hourglass-split"></i></div>
                                <div class="status-count" th:text="${statusCounts.PENDING}">0</div>
                                <div class="status-label">Pending</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card status-card text-center bg-success text-white">
                            <div class="card-body">
                                <div class="status-icon"><i class="bi bi-check-circle"></i></div>
                                <div class="status-count" th:text="${statusCounts.APPROVED}">0</div>
                                <div class="status-label">Approved</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card status-card text-center bg-danger text-white">
                            <div class="card-body">
                                <div class="status-icon"><i class="bi bi-x-circle"></i></div>
                                <div class="status-count" th:text="${statusCounts.REJECTED}">0</div>
                                <div class="status-label">Rejected</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card status-card text-center bg-info text-white">
                            <div class="card-body">
                                <div class="status-icon"><i class="bi bi-check2-all"></i></div>
                                <div class="status-count" th:text="${statusCounts.COMPLETED}">0</div>
                                <div class="status-label">Completed</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card status-card text-center bg-secondary text-white">
                            <div class="card-body">
                                <div class="status-icon"><i class="bi bi-scissors"></i></div>
                                <div class="status-count" th:text="${statusCounts.BOND_BROKEN}">0</div>
                                <div class="status-label">Bond Broken</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- NetCafe Processing Status -->
                <h4 class="mb-3">NetCafe Processing Status</h4>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card status-card text-center bg-warning text-dark">
                            <div class="card-body">
                                <div class="status-icon"><i class="bi bi-hourglass-split"></i></div>
                                <div class="status-count" th:text="${netCafeStatusCounts.PENDING}">0</div>
                                <div class="status-label">Pending with NetCafe</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card status-card text-center bg-primary text-white">
                            <div class="card-body">
                                <div class="status-icon"><i class="bi bi-gear"></i></div>
                                <div class="status-count" th:text="${netCafeStatusCounts.PROCESSING}">0</div>
                                <div class="status-label">Processing by NetCafe</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card status-card text-center bg-info text-white">
                            <div class="card-body">
                                <div class="status-icon"><i class="bi bi-check2-all"></i></div>
                                <div class="status-count" th:text="${netCafeStatusCounts.COMPLETED}">0</div>
                                <div class="status-label">Completed by NetCafe</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card status-card text-center bg-success text-white">
                            <div class="card-body">
                                <div class="status-icon"><i class="bi bi-exclamation-circle"></i></div>
                                <div class="status-count" th:text="${netCafeStatusCounts.UNCLAIMED}">0</div>
                                <div class="status-label">Waiting for NetCafe</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Applications -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">My Recent Applications</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Scheme</th>
                                        <th>Status</th>
                                        <th>Application Date</th>
                                        <th>NetCafe Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="app : ${recentApplications}">
                                        <td th:text="${app.id}">1</td>
                                        <td th:text="${app.scheme != null ? app.scheme.name : 'N/A'}">Scheme Name</td>
                                        <td>
                                            <span class="badge" th:classappend="${
                                                app.status == 'PENDING' ? 'bg-warning' :
                                                app.status == 'APPROVED' ? 'bg-success' :
                                                app.status == 'REJECTED' ? 'bg-danger' :
                                                app.status == 'COMPLETED' ? 'bg-info' :
                                                app.status == 'BOND_BROKEN' ? 'bg-secondary' : 'bg-secondary'
                                            }" th:text="${app.status}">Status</span>
                                        </td>
                                        <td th:text="${#temporals.format(app.applicationDate, 'dd-MM-yyyy')}">01-01-2023</td>
                                        <td>
                                            <span th:if="${app.status == 'APPROVED'}" class="badge bg-warning">Approved</span>
                                            <span th:if="${app.status != 'APPROVED'}" class="badge bg-secondary">N/A</span>
                                        </td>
                                        <td>
                                            <a th:href="@{'/user/application/' + ${app.id}}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                    <tr th:if="${recentApplications.empty}">
                                        <td colspan="6" class="text-center">No applications found</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
