<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - NetCafe User Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            background-color: #343a40;
            color: white;
            min-height: 100vh;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #495057;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 0;
            padding: 20px;
        }
        .welcome-card {
            background-color: #007bff;
            color: white;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .card {
            margin-bottom: 20px;
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .card-header {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            font-weight: bold;
        }
        @media (min-width: 768px) {
            .content {
                margin-left: 16.666667%;
            }
        }
        @media (max-width: 767.98px) {
            .sidebar {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: auto;
                z-index: 1000;
                padding-top: 0;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
        .user-profile-img {
            width: 150px;
            height: 150px;
            object-fit: cover;
            border-radius: 50%;
            border: 5px solid #fff;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .user-info {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        .user-info h3 {
            margin-bottom: 20px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 10px;
        }
        .user-info p {
            margin-bottom: 10px;
        }
        .user-info strong {
            display: inline-block;
            width: 150px;
        }
        .nav-tabs .nav-link {
            color: #495057;
        }
        .nav-tabs .nav-link.active {
            font-weight: bold;
            color: #007bff;
        }
        .document-img {
            max-width: 100%;
            height: auto;
            border-radius: 5px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>

                    <!-- User Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/users}">
                            <i class="bi bi-person"></i> General Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/netcafe-users}">
                            <i class="bi bi-people"></i> NetCafe Users
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/netcafe-approvals}">
                            <i class="bi bi-check2-circle"></i> NetCafe Approvals
                        </a>
                    </li>

                    <!-- Scheme Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes}">
                            <i class="bi bi-list-check"></i> Manage Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes/applications}">
                            <i class="bi bi-file-earmark-text"></i> Scheme Applications
                        </a>
                    </li>

                    <!-- Payment Management Section -->
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/pending-payments}">
                            <i class="bi bi-credit-card"></i> Verify Payments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/commissions}">
                            <i class="bi bi-cash-coin"></i> NetCafe Commissions
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="welcome-card">
                    <h2>NetCafe User Details</h2>
                    <p>View detailed information about the NetCafe user and their applications.</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <a th:href="@{/admin/netcafe-users}" class="btn btn-outline-primary">
                                <i class="bi bi-arrow-left"></i> Back to NetCafe Users
                            </a>
                            <div th:if="${user.approved}">
                                <form th:action="@{/admin/netcafe-users/{id}/toggle-status(id=${user.id})}" method="post" class="d-inline-block">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="bi bi-toggle-on"></i>
                                        <span th:if="${user.active}">Deactivate User</span>
                                        <span th:unless="${user.active}">Activate User</span>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body text-center">
                                <img th:if="${user.photo != null}" th:src="@{/netcafe/photo/{id}(id=${user.id})}" alt="User Profile" class="user-profile-img mb-3">
                                <img th:unless="${user.photo != null}" src="https://via.placeholder.com/150" alt="User Profile" class="user-profile-img mb-3">
                                <h4 th:text="${user.name}">John Doe</h4>
                                <p class="text-muted" th:text="${user.email}"><EMAIL></p>
                                <div class="mt-3">
                                    <span th:if="${user.approved && user.active}" class="badge bg-success">Active</span>
                                    <span th:if="${user.approved && !user.active}" class="badge bg-danger">Inactive</span>
                                    <span th:unless="${user.approved}" class="badge bg-warning">Pending</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                User Information
                            </div>
                            <div class="card-body">
                                <div class="user-info">
                                    <p><strong>User ID:</strong> <span th:text="${user.id}">1</span></p>
                                    <p><strong>Name:</strong> <span th:text="${user.name}">John Doe</span></p>
                                    <p><strong>Email:</strong> <span th:text="${user.email}"><EMAIL></span></p>
                                    <p><strong>Mobile Number:</strong> <span th:text="${user.mobileNumber}">1234567890</span></p>
                                    <p><strong>Aadhar Number:</strong> <span th:text="${user.aadharNumber}">123456789012</span></p>
                                    <p><strong>PAN Number:</strong> <span th:text="${user.panNumber}">**********</span></p>
                                    <p><strong>Registration Date:</strong> <span th:text="${#temporals.format(user.registrationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</span></p>
                                    <p><strong>Status:</strong>
                                        <span th:if="${user.approved && user.active}" class="badge bg-success">Active</span>
                                        <span th:if="${user.approved && !user.active}" class="badge bg-danger">Inactive</span>
                                        <span th:unless="${user.approved}" class="badge bg-warning">Pending</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        Documents
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">Aadhar Card</div>
                                    <div class="card-body text-center">
                                        <img th:if="${user.aadharCardPhoto != null}" th:src="@{/netcafe/aadhar-card/{id}(id=${user.id})}" alt="Aadhar Card" class="document-img">
                                        <p th:unless="${user.aadharCardPhoto != null}" class="text-muted">No Aadhar Card uploaded</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">PAN Card</div>
                                    <div class="card-body text-center">
                                        <img th:if="${user.panCardPhoto != null}" th:src="@{/netcafe/pan-card/{id}(id=${user.id})}" alt="PAN Card" class="document-img">
                                        <p th:unless="${user.panCardPhoto != null}" class="text-muted">No PAN Card uploaded</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header">CSC Certificate</div>
                                    <div class="card-body text-center">
                                        <img th:if="${user.cscCertificate != null}" th:src="@{/netcafe/csc-certificate/{id}(id=${user.id})}" alt="CSC Certificate" class="document-img">
                                        <p th:unless="${user.cscCertificate != null}" class="text-muted">No CSC Certificate uploaded</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        Assigned Applications
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Scheme</th>
                                        <th>User</th>
                                        <th>Assignment Date</th>
                                        <th>Status</th>
                                        <th>Bond Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:if="${applications.empty}">
                                        <td colspan="7" class="text-center">No applications assigned</td>
                                    </tr>
                                    <tr th:each="app : ${applications}">
                                        <td th:text="${app.id}">1</td>
                                        <td th:text="${app.application != null && app.application.scheme != null ? app.application.scheme.name : 'Unknown'}">Scheme Name</td>
                                        <td th:text="${app.application != null && app.application.user != null ? app.application.user.name : 'Unknown'}">User Name</td>
                                        <td th:text="${#temporals.format(app.assignmentDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</td>
                                        <td>
                                            <span th:if="${app.status == 'PENDING'}" class="badge bg-warning">Pending</span>
                                            <span th:if="${app.status == 'PROCESSING'}" class="badge bg-info">Processing</span>
                                            <span th:if="${app.status == 'COMPLETED'}" class="badge bg-success">Completed</span>
                                            <span th:if="${app.status == 'CONFIRMED'}" class="badge bg-primary">Confirmed</span>
                                            <span th:if="${app.status == 'BOND_BROKEN'}" class="badge bg-danger">Bond Broken</span>
                                        </td>
                                        <td>
                                            <span th:if="${app.bondBroken == true}" class="badge bg-danger">Broken</span>
                                            <span th:unless="${app.bondBroken == true}" class="badge bg-success">Active</span>
                                        </td>
                                        <td>
                                            <div class="d-flex">
                                                <a th:if="${app.application != null}" th:href="@{/admin/schemes/applications/{id}(id=${app.application.id})}" class="btn btn-primary btn-sm me-2">
                                                    <i class="bi bi-eye"></i> View
                                                </a>
                                                <a th:if="${app.application != null}" th:href="@{/admin/messages/{id}(id=${app.application.id})}" class="btn btn-info btn-sm me-2">
                                                    <i class="bi bi-chat-dots"></i> Messages
                                                </a>
                                                <form th:if="${!app.bondBroken}" th:action="@{/admin/applications/{id}/break-bond(id=${app.application.id})}" method="post"
                                                      onsubmit="return confirm('Are you sure you want to break the bond?');">
                                                    <button type="submit" class="btn btn-danger btn-sm">
                                                        <i class="bi bi-scissors"></i> Break Bond
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
