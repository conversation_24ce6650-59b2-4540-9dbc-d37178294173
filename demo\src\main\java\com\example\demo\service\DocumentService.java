package com.example.demo.service;

import com.example.demo.model.Document;
import com.example.demo.repository.DocumentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class DocumentService {

    @Autowired
    private DocumentRepository documentRepository;

    public List<Document> getAllDocuments() {
        return documentRepository.findAll();
    }

    public List<Document> getAllActiveDocuments() {
        return documentRepository.findByActiveTrue();
    }

    public List<Document> getDocumentsByCategory(String category) {
        return documentRepository.findByCategoryOrderByNameAsc(category);
    }

    public List<Document> getCustomDocuments() {
        return documentRepository.findByIsCustomTrue();
    }

    public Optional<Document> getDocumentById(Long id) {
        return documentRepository.findById(id);
    }

    @Transactional
    public Document createDocument(Document document) {
        return documentRepository.save(document);
    }

    @Transactional
    public Document updateDocument(Document document) {
        return documentRepository.save(document);
    }

    @Transactional
    public void deleteDocument(Long id) {
        documentRepository.deleteById(id);
    }

    public boolean isDocumentNameTaken(String name) {
        return documentRepository.existsByName(name);
    }

    @Transactional
    public void initializeDefaultDocuments() {
        if (documentRepository.count() == 0) {
            // Essential Identity and Address Documents
            createDefaultDocument("Aadhaar Card", "Universal identity and biometric verification", "Essential Identity and Address Documents");
            createDefaultDocument("PAN Card", "Required for financial transactions and taxation", "Essential Identity and Address Documents");
            createDefaultDocument("Voter ID (EPIC)", "For voting and as an ID proof", "Essential Identity and Address Documents");
            createDefaultDocument("Passport", "For international travel and identity", "Essential Identity and Address Documents");
            createDefaultDocument("Driving Licence", "Identity, address proof, and for driving legally", "Essential Identity and Address Documents");
            createDefaultDocument("Ration Card", "Availing subsidized food and government benefits", "Essential Identity and Address Documents");
            createDefaultDocument("Birth Certificate", "Proof of birth, essential for school, passport, etc", "Essential Identity and Address Documents");
            createDefaultDocument("Caste Certificate (SC/ST/OBC)", "For reservations and benefits", "Essential Identity and Address Documents");
            createDefaultDocument("Income Certificate", "To apply for subsidies, scholarships, and EWS quota", "Essential Identity and Address Documents");
            createDefaultDocument("Domicile Certificate", "Proof of residence in a state for state quotas, jobs", "Essential Identity and Address Documents");
            createDefaultDocument("Marriage Certificate", "Legal proof of marriage", "Essential Identity and Address Documents");
            
            // Education-Related Documents
            createDefaultDocument("Mark Sheets (10th, 12th, Graduation)", "Academic records", "Education-Related Documents");
            createDefaultDocument("Transfer Certificate (TC)", "School transfer proof", "Education-Related Documents");
            createDefaultDocument("Migration Certificate", "For changing educational boards/universities", "Education-Related Documents");
            createDefaultDocument("Character Certificate", "Proof of good conduct", "Education-Related Documents");
            createDefaultDocument("Bonafide Certificate", "Proof of enrollment in educational institution", "Education-Related Documents");
            createDefaultDocument("School Leaving Certificate", "Proof of completing education", "Education-Related Documents");
            
            // Employment and Professional Documents
            createDefaultDocument("Employment ID or Offer Letter", "Proof of employment", "Employment and Professional Documents");
            createDefaultDocument("Experience Certificate", "Proof of work experience", "Employment and Professional Documents");
            createDefaultDocument("Resume/CV", "Professional profile", "Employment and Professional Documents");
            createDefaultDocument("Professional Licenses", "Professional qualifications", "Employment and Professional Documents");
            
            // Property and Housing Documents
            createDefaultDocument("Property Deed / Registry Papers", "Proof of property ownership", "Property and Housing Documents");
            createDefaultDocument("Encumbrance Certificate", "Property free from legal claims", "Property and Housing Documents");
            createDefaultDocument("Electricity/Water Bill", "Utility bills as address proof", "Property and Housing Documents");
            createDefaultDocument("House Tax Receipt", "Property tax payment proof", "Property and Housing Documents");
            createDefaultDocument("Rent Agreement", "Rental property documentation", "Property and Housing Documents");
            
            // Financial Documents
            createDefaultDocument("Bank Passbook / Statement", "Banking transaction records", "Financial Documents");
            createDefaultDocument("Cancelled Cheque", "Banking verification", "Financial Documents");
            createDefaultDocument("Salary Slips", "Income proof", "Financial Documents");
            createDefaultDocument("Form 16", "Tax deduction certificate", "Financial Documents");
            createDefaultDocument("ITR Acknowledgement", "Income tax return proof", "Financial Documents");
            createDefaultDocument("Loan Documents", "Loan agreement papers", "Financial Documents");
            
            // Health and Insurance Documents
            createDefaultDocument("Health Card", "Health scheme enrollment proof", "Health and Insurance Documents");
            createDefaultDocument("Medical Reports/Prescriptions", "Health records", "Health and Insurance Documents");
            createDefaultDocument("Health Insurance Policy", "Health coverage proof", "Health and Insurance Documents");
            createDefaultDocument("Disability Certificate", "Disability proof for benefits", "Health and Insurance Documents");
            
            // Government Scheme Documents
            createDefaultDocument("MGNREGA Job Card", "Rural employment scheme card", "Government Scheme Documents");
            createDefaultDocument("BPL Card", "Below Poverty Line status proof", "Government Scheme Documents");
            createDefaultDocument("Jan Dhan Bank Account Documents", "Financial inclusion scheme", "Government Scheme Documents");
            createDefaultDocument("Kisan Credit Card", "Agricultural credit card", "Government Scheme Documents");
            createDefaultDocument("PM Kisan Scheme Registration", "Farmer welfare scheme", "Government Scheme Documents");
            createDefaultDocument("Ujjwala Yojana LPG Connection Documents", "LPG subsidy scheme", "Government Scheme Documents");
        }
    }

    private void createDefaultDocument(String name, String description, String category) {
        Document document = new Document();
        document.setName(name);
        document.setDescription(description);
        document.setCategory(category);
        document.setCustom(false);
        document.setActive(true);
        documentRepository.save(document);
    }
}
