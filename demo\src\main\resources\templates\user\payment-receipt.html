<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .receipt-container {
            max-width: 800px;
            margin: 50px auto;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .receipt-header {
            border-bottom: 2px solid #28a745;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }
        .receipt-title {
            color: #28a745;
            font-weight: bold;
        }
        .receipt-logo {
            font-size: 2rem;
            color: #28a745;
        }
        .receipt-body {
            padding: 20px 0;
        }
        .receipt-footer {
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
            margin-top: 20px;
            font-size: 0.9rem;
            color: #6c757d;
        }
        .receipt-amount {
            font-size: 2rem;
            font-weight: bold;
            color: #28a745;
        }
        .receipt-status {
            font-size: 1.2rem;
            font-weight: bold;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
        }
        .status-completed {
            background-color: #d4edda;
            color: #28a745;
        }
        .receipt-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .receipt-info p {
            margin-bottom: 10px;
        }
        .receipt-actions {
            margin-top: 30px;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                background-color: white;
            }
            .receipt-container {
                box-shadow: none;
                margin: 0;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="receipt-container">
        <div class="receipt-header d-flex justify-content-between align-items-center">
            <div>
                <h1 class="receipt-title">Payment Receipt</h1>
                <p class="text-muted mb-0">Official payment confirmation</p>
            </div>
            <div class="receipt-logo">
                <i class="bi bi-check-circle-fill"></i>
            </div>
        </div>

        <div class="receipt-body">
            <div class="alert alert-success" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i> This payment has been verified and confirmed.
            </div>

            <div class="receipt-info">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Receipt ID:</strong> <span th:text="${payment.id}">12345</span></p>
                        <p><strong>Date:</strong> <span th:text="${#temporals.format(payment.paymentDate, 'dd-MM-yyyy HH:mm')}">01-01-2023 12:30</span></p>
                        <p><strong>Payment Method:</strong> <span th:text="${payment.paymentMethod}">UPI</span></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Transaction ID:</strong> <span th:text="${payment.transactionId}">TXN123456</span></p>
                        <p><strong>Status:</strong> <span class="receipt-status status-completed">Verified</span></p>
                        <p><strong>Verification Date:</strong> <span th:text="${#temporals.format(payment.settlementDate, 'dd-MM-yyyy HH:mm')}">02-01-2023 10:15</span></p>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <h5>Payment For</h5>
                    <p><strong>Scheme:</strong> <span th:text="${payment.application != null && payment.application.scheme != null ? payment.application.scheme.name : 'Unknown'}">Scheme Name</span></p>
                    <p><strong>Application ID:</strong> <span th:text="${payment.application != null ? payment.application.id : 'Unknown'}">APP123</span></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <h5>Amount Paid</h5>
                    <p class="receipt-amount">₹<span th:text="${payment.amount}">500.00</span></p>
                </div>
            </div>

            <div class="receipt-footer">
                <p class="mb-1">This is an electronically generated receipt. No signature is required.</p>
                <p class="mb-1">For any queries related to this payment, please contact the admin with your Receipt ID.</p>
                <p class="mb-0">Thank you for your payment.</p>
            </div>

            <div class="receipt-actions no-print text-center mt-4">
                <button class="btn btn-success me-2" onclick="window.print()">
                    <i class="bi bi-printer"></i> Print Receipt
                </button>
                <a th:href="@{/user/payments}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Payment History
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
