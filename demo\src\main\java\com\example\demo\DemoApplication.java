package com.example.demo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;

@SpringBootApplication
public class DemoApplication {

	public static void main(String[] args) {
		SpringApplication.run(DemoApplication.class, args);
	}

	@EventListener(ApplicationReadyEvent.class)
	public void applicationReady() {
		System.out.println("=======================================================");
		System.out.println("NetCafe Management System is now running!");
		System.out.println("Access the application at: http://localhost:8080");
		System.out.println("Default admin credentials:");
		System.out.println("Username: admin");
		System.out.println("Password: admin123");
		System.out.println("=======================================================");
	}
}
