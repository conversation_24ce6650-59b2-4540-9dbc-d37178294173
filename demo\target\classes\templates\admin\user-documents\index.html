<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Documents - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #007bff;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes/applications}">
                            <i class="bi bi-file-earmark-text"></i> Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes}">
                            <i class="bi bi-list-check"></i> Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/bonds}">
                            <i class="bi bi-scissors"></i> Bond Management
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/status}">
                            <i class="bi bi-graph-up"></i> Status Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/documents}">
                            <i class="bi bi-file-earmark"></i> Documents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/user-documents}">
                            <i class="bi bi-file-earmark-check"></i> User Documents
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 content">
                <div class="header-card">
                    <h2><i class="bi bi-file-earmark-check"></i> User Documents</h2>
                    <p>Verify and manage user-uploaded documents</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <!-- Unverified Documents -->
                <div class="card mb-4">
                    <div class="card-header bg-warning">
                        <h5 class="mb-0">Pending Verification</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Document Type</th>
                                        <th>File Name</th>
                                        <th>Upload Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="doc : ${unverifiedDocuments}">
                                        <td th:text="${doc.id}">1</td>
                                        <td th:text="${doc.user.name}">User Name</td>
                                        <td th:text="${doc.document.name}">Document Type</td>
                                        <td th:text="${doc.documentName}">document.pdf</td>
                                        <td th:text="${#temporals.format(doc.uploadDate, 'dd-MM-yyyy')}">01-01-2023</td>
                                        <td>
                                            <a th:href="@{'/admin/user-documents/' + ${doc.id}}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i> View
                                            </a>
                                            <a th:href="@{'/admin/user-documents/' + ${doc.id} + '/download'}" class="btn btn-sm btn-success">
                                                <i class="bi bi-download"></i> Download
                                            </a>
                                        </td>
                                    </tr>
                                    <tr th:if="${unverifiedDocuments.empty}">
                                        <td colspan="6" class="text-center">No documents pending verification</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
