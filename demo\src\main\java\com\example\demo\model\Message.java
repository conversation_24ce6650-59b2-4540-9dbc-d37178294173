package com.example.demo.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "messages")
public class Message {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "netcafe_application_id", nullable = false)
    private NetCafeApplication netCafeApplication;

    @Column(nullable = false)
    private String senderType; // "USER" or "NETCAFE"

    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;

    @Column(nullable = false)
    private LocalDateTime sentDate;

    @Column
    private LocalDateTime readDate;

    @Column(nullable = false)
    private boolean isRead;

    // File attachment fields
    @Lob
    @Column(columnDefinition = "LONGBLOB")
    private byte[] attachment;

    @Column
    private String attachmentName;

    @Column
    private String attachmentContentType;

    @Column
    private Long attachmentSize;

    @Column
    private String attachmentType; // "IMAGE", "PDF", "DOCUMENT", etc.

    // Default constructor
    public Message() {
        this.sentDate = LocalDateTime.now();
        this.isRead = false;
    }

    // Constructor with fields
    public Message(NetCafeApplication netCafeApplication, String senderType, String content) {
        this.netCafeApplication = netCafeApplication;
        this.senderType = senderType;
        this.content = content;
        this.sentDate = LocalDateTime.now();
        this.isRead = false;
    }

    // Constructor with attachment
    public Message(NetCafeApplication netCafeApplication, String senderType, String content,
                  byte[] attachment, String attachmentName, String attachmentContentType,
                  Long attachmentSize, String attachmentType) {
        this.netCafeApplication = netCafeApplication;
        this.senderType = senderType;
        this.content = content;
        this.sentDate = LocalDateTime.now();
        this.isRead = false;
        this.attachment = attachment;
        this.attachmentName = attachmentName;
        this.attachmentContentType = attachmentContentType;
        this.attachmentSize = attachmentSize;
        this.attachmentType = attachmentType;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public NetCafeApplication getNetCafeApplication() {
        return netCafeApplication;
    }

    public void setNetCafeApplication(NetCafeApplication netCafeApplication) {
        this.netCafeApplication = netCafeApplication;
    }

    public String getSenderType() {
        return senderType;
    }

    public void setSenderType(String senderType) {
        this.senderType = senderType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public LocalDateTime getSentDate() {
        return sentDate;
    }

    public void setSentDate(LocalDateTime sentDate) {
        this.sentDate = sentDate;
    }

    public LocalDateTime getReadDate() {
        return readDate;
    }

    public void setReadDate(LocalDateTime readDate) {
        this.readDate = readDate;
    }

    public boolean isRead() {
        return isRead;
    }

    public void setRead(boolean read) {
        isRead = read;
        if (read && readDate == null) {
            this.readDate = LocalDateTime.now();
        }
    }

    public byte[] getAttachment() {
        return attachment;
    }

    public void setAttachment(byte[] attachment) {
        this.attachment = attachment;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    public String getAttachmentContentType() {
        return attachmentContentType;
    }

    public void setAttachmentContentType(String attachmentContentType) {
        this.attachmentContentType = attachmentContentType;
    }

    public Long getAttachmentSize() {
        return attachmentSize;
    }

    public void setAttachmentSize(Long attachmentSize) {
        this.attachmentSize = attachmentSize;
    }

    public String getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(String attachmentType) {
        this.attachmentType = attachmentType;
    }

    public boolean hasAttachment() {
        return attachment != null && attachmentName != null && attachmentContentType != null;
    }
}
