package com.example.demo.controller;

import com.example.demo.model.Admin;
import com.example.demo.model.Document;
import com.example.demo.service.DocumentService;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping("/admin/documents")
public class DocumentManagementController {

    @Autowired
    private DocumentService documentService;

    @GetMapping
    public String listDocuments(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");
        
        if (admin == null) {
            return "redirect:/admin/login";
        }
        
        List<Document> documents = documentService.getAllDocuments();
        model.addAttribute("admin", admin);
        model.addAttribute("documents", documents);
        
        return "admin/documents/list";
    }

    @GetMapping("/create")
    public String showCreateForm(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");
        
        if (admin == null) {
            return "redirect:/admin/login";
        }
        
        model.addAttribute("admin", admin);
        model.addAttribute("document", new Document());
        
        return "admin/documents/create";
    }

    @PostMapping("/create")
    public String createDocument(
            @ModelAttribute Document document,
            HttpSession session,
            RedirectAttributes redirectAttributes) {
        
        Admin admin = (Admin) session.getAttribute("admin");
        
        if (admin == null) {
            return "redirect:/admin/login";
        }
        
        try {
            // Set as custom document
            document.setCustom(true);
            documentService.createDocument(document);
            redirectAttributes.addFlashAttribute("success", "Document type created successfully");
            return "redirect:/admin/documents";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error creating document type: " + e.getMessage());
            return "redirect:/admin/documents/create";
        }
    }

    @GetMapping("/edit/{id}")
    public String showEditForm(
            @PathVariable Long id,
            HttpSession session,
            Model model,
            RedirectAttributes redirectAttributes) {
        
        Admin admin = (Admin) session.getAttribute("admin");
        
        if (admin == null) {
            return "redirect:/admin/login";
        }
        
        Optional<Document> documentOpt = documentService.getDocumentById(id);
        
        if (documentOpt.isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "Document type not found");
            return "redirect:/admin/documents";
        }
        
        model.addAttribute("admin", admin);
        model.addAttribute("document", documentOpt.get());
        
        return "admin/documents/edit";
    }

    @PostMapping("/edit/{id}")
    public String updateDocument(
            @PathVariable Long id,
            @ModelAttribute Document document,
            HttpSession session,
            RedirectAttributes redirectAttributes) {
        
        Admin admin = (Admin) session.getAttribute("admin");
        
        if (admin == null) {
            return "redirect:/admin/login";
        }
        
        try {
            Optional<Document> existingDocumentOpt = documentService.getDocumentById(id);
            
            if (existingDocumentOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Document type not found");
                return "redirect:/admin/documents";
            }
            
            Document existingDocument = existingDocumentOpt.get();
            existingDocument.setName(document.getName());
            existingDocument.setDescription(document.getDescription());
            existingDocument.setCategory(document.getCategory());
            existingDocument.setActive(document.isActive());
            
            documentService.updateDocument(existingDocument);
            redirectAttributes.addFlashAttribute("success", "Document type updated successfully");
            return "redirect:/admin/documents";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error updating document type: " + e.getMessage());
            return "redirect:/admin/documents/edit/" + id;
        }
    }

    @PostMapping("/delete/{id}")
    public String deleteDocument(
            @PathVariable Long id,
            HttpSession session,
            RedirectAttributes redirectAttributes) {
        
        Admin admin = (Admin) session.getAttribute("admin");
        
        if (admin == null) {
            return "redirect:/admin/login";
        }
        
        try {
            documentService.deleteDocument(id);
            redirectAttributes.addFlashAttribute("success", "Document type deleted successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error deleting document type: " + e.getMessage());
        }
        
        return "redirect:/admin/documents";
    }
}
